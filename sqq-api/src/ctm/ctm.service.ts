import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ctm } from './entities/ctm.entity';
import { User } from '../users/entities/user.entity';
import { CreateCtmDto } from './dto/create-ctm.dto';
import { UpdateCtmDto } from './dto/update-ctm.dto';
import { GetCtmsDto } from './dto/get-ctms.dto';
import { AssignCtmDto, AssignUsersToCtmDto } from './dto/assign-ctm.dto';
import { CtmResponseDto } from './dto/ctm-response.dto';
import { EncryptionService } from '../common/services/encryption.service';
import { plainToClass } from 'class-transformer';

@Injectable()
export class CtmService {
  private readonly logger = new Logger(CtmService.name);

  constructor(
    @InjectRepository(Ctm)
    private ctmRepository: Repository<Ctm>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private encryptionService: EncryptionService,
  ) {}

  async create(createCtmDto: CreateCtmDto): Promise<CtmResponseDto> {
    this.logger.log(`Creating CTM: ${createCtmDto.name}`);

    // Check if CTM name already exists
    const existingCtm = await this.ctmRepository.findOne({
      where: { name: createCtmDto.name },
    });

    if (existingCtm) {
      throw new ConflictException('CTM name already exists');
    }

    const ctmData = { ...createCtmDto };

    // Encrypt sensitive data
    if (ctmData.password) {
      ctmData.password = this.encryptionService.encrypt(ctmData.password);
    }

    if (ctmData.seqrngApiToken) {
      ctmData.seqrngApiToken = this.encryptionService.encrypt(ctmData.seqrngApiToken);
    }

    const ctm = this.ctmRepository.create(ctmData);
    const savedCtm = await this.ctmRepository.save(ctm);

    return plainToClass(CtmResponseDto, savedCtm, { excludeExtraneousValues: true });
  }

  async findAll(getCtmsDto: GetCtmsDto): Promise<{
    ctms: CtmResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    this.logger.log('Getting all CTMs');

    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = getCtmsDto;

    const queryBuilder = this.ctmRepository.createQueryBuilder('ctm');

    // Apply filters
    if (search) {
      queryBuilder.andWhere(
        '(ctm.name ILIKE :search OR ctm.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('ctm.isActive = :isActive', { isActive });
    }

    // Apply sorting
    queryBuilder.orderBy(`ctm.${sortBy}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [ctms, total] = await queryBuilder.getManyAndCount();

    return {
      ctms: ctms.map(ctm => plainToClass(CtmResponseDto, ctm, { excludeExtraneousValues: true })),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string): Promise<CtmResponseDto> {
    this.logger.log(`Getting CTM: ${id}`);

    const ctm = await this.ctmRepository.findOne({
      where: { id },
      relations: ['users'],
    });

    if (!ctm) {
      throw new NotFoundException('CTM not found');
    }

    return plainToClass(CtmResponseDto, ctm, { excludeExtraneousValues: true });
  }

  async update(id: string, updateCtmDto: UpdateCtmDto): Promise<CtmResponseDto> {
    this.logger.log(`Updating CTM: ${id}`);

    const ctm = await this.ctmRepository.findOne({ where: { id } });

    if (!ctm) {
      throw new NotFoundException('CTM not found');
    }

    // Check if name is being updated and if it already exists
    if (updateCtmDto.name && updateCtmDto.name !== ctm.name) {
      const existingCtm = await this.ctmRepository.findOne({
        where: { name: updateCtmDto.name },
      });

      if (existingCtm) {
        throw new ConflictException('CTM name already exists');
      }
    }

    const updateData = { ...updateCtmDto };

    // Encrypt sensitive data if provided
    if (updateData.password) {
      updateData.password = this.encryptionService.encrypt(updateData.password);
    }

    if (updateData.seqrngApiToken) {
      updateData.seqrngApiToken = this.encryptionService.encrypt(updateData.seqrngApiToken);
    }

    await this.ctmRepository.update(id, updateData);
    const updatedCtm = await this.ctmRepository.findOne({ where: { id } });

    return plainToClass(CtmResponseDto, updatedCtm, { excludeExtraneousValues: true });
  }

  async remove(id: string): Promise<void> {
    this.logger.log(`Removing CTM: ${id}`);

    const ctm = await this.ctmRepository.findOne({
      where: { id },
      relations: ['keys'],
    });

    if (!ctm) {
      throw new NotFoundException('CTM not found');
    }

    // Check if CTM has associated keys
    if (ctm.keys && ctm.keys.length > 0) {
      throw new BadRequestException('Cannot delete CTM with associated keys');
    }

    await this.ctmRepository.remove(ctm);
  }

  /**
   * Assign CTMs to a user
   */
  async assignCtmsToUser(userId: string, assignCtmDto: AssignCtmDto): Promise<void> {
    this.logger.log(`Assigning CTMs to user: ${userId}`);

    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['ctms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Validate that all CTMs exist and are active
    const ctms = await this.ctmRepository.findByIds(assignCtmDto.ctmIds);

    if (ctms.length !== assignCtmDto.ctmIds.length) {
      throw new BadRequestException('One or more CTMs not found');
    }

    const inactiveCtms = ctms.filter(ctm => !ctm.isActive);
    if (inactiveCtms.length > 0) {
      throw new BadRequestException('Cannot assign inactive CTMs');
    }

    // Replace current CTM assignments
    user.ctms = ctms;
    await this.userRepository.save(user);
  }

  /**
   * Assign users to a CTM
   */
  async assignUsersToCtm(ctmId: string, assignUsersDto: AssignUsersToCtmDto): Promise<void> {
    this.logger.log(`Assigning users to CTM: ${ctmId}`);

    const ctm = await this.ctmRepository.findOne({
      where: { id: ctmId },
      relations: ['users'],
    });

    if (!ctm) {
      throw new NotFoundException('CTM not found');
    }

    if (!ctm.isActive) {
      throw new BadRequestException('Cannot assign users to inactive CTM');
    }

    // Validate that all users exist and are active
    const users = await this.userRepository.findByIds(assignUsersDto.userIds);

    if (users.length !== assignUsersDto.userIds.length) {
      throw new BadRequestException('One or more users not found');
    }

    const inactiveUsers = users.filter(user => !user.isActive);
    if (inactiveUsers.length > 0) {
      throw new BadRequestException('Cannot assign inactive users');
    }

    // Add users to CTM (append, don't replace)
    const existingUserIds = ctm.users.map(u => u.id);
    const newUsers = users.filter(user => !existingUserIds.includes(user.id));

    ctm.users = [...ctm.users, ...newUsers];
    await this.ctmRepository.save(ctm);
  }

  /**
   * Remove CTM assignment from user
   */
  async removeCtmFromUser(userId: string, ctmId: string): Promise<void> {
    this.logger.log(`Removing CTM ${ctmId} from user ${userId}`);

    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['ctms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.ctms = user.ctms.filter(ctm => ctm.id !== ctmId);
    await this.userRepository.save(user);
  }

  /**
   * Get CTMs assigned to a user
   */
  async getUserCtms(userId: string): Promise<CtmResponseDto[]> {
    this.logger.log(`Getting CTMs for user: ${userId}`);

    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['ctms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user.ctms
      .filter(ctm => ctm.isActive)
      .map(ctm => plainToClass(CtmResponseDto, ctm, { excludeExtraneousValues: true }));
  }

  /**
   * Get CTM configuration with decrypted credentials
   */
  async getCtmConfig(ctmId: string): Promise<{
    ipAddress: string;
    username: string;
    password: string;
    domain: string;
  } | null> {
    const ctm = await this.ctmRepository.findOne({
      where: { id: ctmId, isActive: true },
      select: ['ipAddress', 'username', 'password', 'domain'],
    });

    if (!ctm || !ctm.hasCompleteConfiguration) {
      return null;
    }

    return {
      ipAddress: ctm.ipAddress,
      username: ctm.username,
      password: this.encryptionService.decrypt(ctm.password),
      domain: ctm.domain,
    };
  }

  /**
   * Get CTM SeqRNG configuration with decrypted token
   */
  async getCtmSeqrngConfig(ctmId: string): Promise<{
    ipAddress: string;
    apiToken: string;
  } | null> {
    const ctm = await this.ctmRepository.findOne({
      where: { id: ctmId, isActive: true },
      select: ['seqrngIpAddress', 'seqrngApiToken'],
    });

    if (!ctm || !ctm.hasSeqrngConfiguration) {
      return null;
    }

    return {
      ipAddress: ctm.seqrngIpAddress!,
      apiToken: this.encryptionService.decrypt(ctm.seqrngApiToken!),
    };
  }
}
