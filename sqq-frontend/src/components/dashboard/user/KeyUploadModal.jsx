import { useState, useEffect, useRef } from 'react';
import { Upload, Key, Settings, Hash, X, ChevronDown, MessageSquare } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * @fileoverview Modal para subir nuevas llaves cuánticas al sistema CTM/HSM
 * @description Componente modal especializado para la carga de llaves cuánticas
 * con selección de algoritmos, validación de datos y configuración avanzada.
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Modal para subir nuevas llaves cuánticas al sistema CTM/HSM
 * @component
 * @param {Object} props - Propiedades del componente
 * @param {boolean} props.isOpen - Controla la visibilidad del modal
 * @param {Function} props.onClose - Función para cerrar el modal
 * @param {Function} props.onSubmit - Función callback para procesar la subida
 * @param {boolean} props.darkMode - Modo oscuro activado
 * @param {string} props.selectedService - Servicio seleccionado (CTM o HSM)
 * @returns {JSX.Element} Modal de subida de llaves cuánticas
 *
 * @example
 * <KeyUploadModal
 *   isOpen={showUpload}
 *   onClose={() => setShowUpload(false)}
 *   onSubmit={handleKeyUpload}
 *   darkMode={isDark}
 *   selectedService="CTM 1"
 * />
 */
const KeyUploadModal = ({ isOpen, onClose, onUpload, darkMode, selectedService = null }) => {
  const [formData, setFormData] = useState({
    key_name: '',
    algorithm: 'AES',
    num_bytes: 32,
    exportable: true,
    message: '' // Campo para mensaje en HSM
  });
  const [isUploading, setIsUploading] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isBytesDropdownOpen, setIsBytesDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const bytesDropdownRef = useRef(null);
  const { t } = useLanguage();

  // Determinar si el servicio seleccionado es HSM
  const isHSMService = selectedService && (
    (typeof selectedService === 'object' && selectedService.type === 'HSM') ||
    (typeof selectedService === 'string' && selectedService.startsWith('HSM'))
  );

  // Cerrar dropdowns al hacer click fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
      if (bytesDropdownRef.current && !bytesDropdownRef.current.contains(event.target)) {
        setIsBytesDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Opciones de algoritmos según el servicio seleccionado
  const algorithmOptions = isHSMService ? [
    { value: 'AES', label: 'AES' },
    { value: 'RSA', label: 'RSA' },
    { value: 'DILITHIUM', label: 'DILITHIUM' },
    { value: 'KYBER', label: 'KYBER' }
  ] : [
    { value: 'AES', label: 'AES' },
    { value: 'ARIA', label: 'ARIA' },
    { value: 'RSA', label: 'RSA' },
    { value: 'HMAC-SHA1', label: 'HMAC-SHA1' },
    { value: 'HMAC-SHA256', label: 'HMAC-SHA256' },
    { value: 'HMAC-SHA384', label: 'HMAC-SHA384' },
    { value: 'HMAC-SHA512', label: 'HMAC-SHA512' }
  ];

  // Resetear algoritmo cuando cambie el servicio para evitar algoritmos no válidos
  useEffect(() => {
    const currentOptions = algorithmOptions.map(option => option.value);
    if (!currentOptions.includes(formData.algorithm)) {
      // Si el algoritmo actual no está disponible en el nuevo servicio, resetear a AES
      setFormData(prev => ({
        ...prev,
        algorithm: 'AES',
        num_bytes: isHSMService ? 16 : 32 // Usar valores por defecto apropiados
      }));
    }
  }, [isHSMService, algorithmOptions]);

  // Valores permitidos por algoritmo según CTM
  const algorithmLimits = {
    AES: { values: [16, 24, 32], label: 'AES' },
    ARIA: { values: [16, 24, 32], label: 'ARIA' },
    RSA: { values: [64, 128, 256, 384, 512], label: 'RSA' },
    'HMAC-SHA1': { values: [16, 24, 32], label: 'HMAC-SHA1' },
    'HMAC-SHA256': { values: [16, 24, 32, 64], label: 'HMAC-SHA256' },
    'HMAC-SHA384': { values: [24, 36, 48], label: 'HMAC-SHA384' },
    'HMAC-SHA512': { values: [32, 48, 64], label: 'HMAC-SHA512' }
  };

  // Valores permitidos por algoritmo según HSM
  const hsmAlgorithmLimits = {
    AES: { values: [16, 24, 32], label: 'AES' },
    RSA: { values: [64, 128, 256, 384, 512], label: 'RSA' },
    DILITHIUM: { values: [1312, 1952, 2592], label: 'DILITHIUM' },
    KYBER: { values: [800, 1184, 1568], label: 'KYBER' }
  };

  // Usar los límites apropiados según el servicio seleccionado
  const currentAlgorithmLimits = isHSMService ? hsmAlgorithmLimits : algorithmLimits;

  const handleChange = (field, value) => {
    if (field === 'algorithm') {
      // Cuando cambia el algoritmo, usar el valor por defecto o el primer valor válido
      const validValues = currentAlgorithmLimits[value].values;
      const currentBytes = formData.num_bytes;
      let newBytes;

      if (value === 'RSA') {
        // Para RSA, usar 128 bytes (1024 bits) como default
        newBytes = 128;
      } else if (value === 'DILITHIUM') {
        // Para DILITHIUM, usar 1312 bytes como default
        newBytes = 1312;
      } else if (value === 'KYBER') {
        // Para KYBER, usar 800 bytes como default
        newBytes = 800;
      } else {
        newBytes = validValues.includes(currentBytes) ? currentBytes : validValues[0];
      }

      setFormData(prev => ({
        ...prev,
        [field]: value,
        num_bytes: newBytes
      }));
      setValidationError('');
    } else if (field === 'num_bytes') {
      // Validar que el valor esté en la lista permitida
      const validValues = currentAlgorithmLimits[formData.algorithm].values;
      if (!validValues.includes(value)) {
        setValidationError(`${formData.algorithm} solo permite: ${validValues.join(', ')} bytes`);
        return;
      }
      setFormData(prev => ({ ...prev, [field]: value }));
      setValidationError('');
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validationError) {
      return;
    }

    setIsUploading(true);
    
    try {
      // Para HSM, no incluir el campo exportable
      const keyData = isHSMService 
        ? { ...formData, exportable: undefined }
        : formData;
        
      await onUpload(keyData);
      setFormData({
        key_name: '',
        algorithm: 'AES',
        num_bytes: 32,
        exportable: true,
        message: '' // Resetear el mensaje
      });
    } catch (error) {
      console.error('Error uploading key:', error);
    } finally {
      setIsUploading(false);
    }
  };

  // Obtener el título dinámico según el servicio seleccionado
  const getModalTitle = () => {
    if (isHSMService) {
      return t('keys.upload.titleHSM');
    }
    return t('keys.upload.title');
  };

  // Obtener el subtítulo dinámico según el servicio seleccionado
  const getModalSubtitle = () => {
    if (isHSMService) {
      return t('keys.upload.subtitleHSM');
    }
    return t('keys.upload.subtitle');
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <Upload size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {getModalTitle()}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {getModalSubtitle()}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >

      <div className="space-y-3 sm:space-y-4">
        {/* Información de la Llave */}
        <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Key size={16} className="text-orange-500 sm:w-5 sm:h-5" />
            {t('keys.upload.keyInfo')}
          </h4>

          <div>
            <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
              {t('keys.upload.keyName')} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.key_name}
              onChange={(e) => handleChange('key_name', e.target.value)}
              placeholder={t('keys.upload.keyNamePlaceholder')}
              required
              disabled={isUploading}
              className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                  : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
              } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
            />
            <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2">
              {t('keys.upload.keyNameHelp')}
            </p>
          </div>
        </div>


        {/* Configuración Técnica */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Settings size={18} className="text-purple-500" />
            {t('keys.upload.technicalConfig')}
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                {t('keys.upload.algorithm')}
              </label>
              <div className="relative" ref={dropdownRef}>
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  disabled={isUploading}
                  className={`w-full px-4 py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                      : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                  } ${isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span>{formData.algorithm}</span>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''} ${
                      darkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}
                  />
                </button>

                {isDropdownOpen && (
                  <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700'
                      : 'border-gray-300 bg-white'
                  }`}>
                    {algorithmOptions.map(option => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => {
                          handleChange('algorithm', option.value);
                          setIsDropdownOpen(false);
                        }}
                        className={`w-full px-4 py-3 text-left font-light tracking-wide transition-colors duration-200 flex items-center justify-between ${
                          formData.algorithm === option.value
                            ? darkMode
                              ? 'bg-purple-900/30 text-purple-300'
                              : 'bg-purple-50 text-purple-700'
                            : darkMode
                              ? 'text-white hover:bg-gray-600'
                              : 'text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        <span>{option.label}</span>
                        <span className="text-xs opacity-70">
                          {currentAlgorithmLimits[option.value].values.join(', ')} bytes
                        </span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2">
                <span className="font-medium">{formData.algorithm}</span> ➔ {t('keys.upload.allowedValues', { values: currentAlgorithmLimits[formData.algorithm].values.join(', ') })}
              </p>
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                {t('keys.upload.keySize')}
              </label>
              <div className="relative" ref={bytesDropdownRef}>
                <button
                  type="button"
                  onClick={() => setIsBytesDropdownOpen(!isBytesDropdownOpen)}
                  disabled={isUploading}
                  className={`w-full px-4 py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                    validationError
                      ? darkMode
                        ? 'border-red-500 bg-gray-700 text-white hover:bg-gray-600'
                        : 'border-red-500 bg-white text-gray-900 hover:bg-gray-50'
                      : darkMode
                        ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                        : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                  } ${isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span>{formData.num_bytes} bytes</span>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${
                      isBytesDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                {isBytesDropdownOpen && (
                  <div className={`absolute z-10 w-full mt-1 border rounded-lg shadow-lg ${
                    darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                  }`}>
                    {currentAlgorithmLimits[formData.algorithm].values.map((bytes) => (
                      <button
                        key={bytes}
                        type="button"
                        onClick={() => {
                          handleChange('num_bytes', bytes);
                          setIsBytesDropdownOpen(false);
                        }}
                        className={`w-full px-4 py-3 text-left font-light tracking-wide transition-colors duration-200 flex items-center justify-between ${
                          formData.num_bytes === bytes
                            ? darkMode
                              ? 'bg-purple-900/30 text-purple-300'
                              : 'bg-purple-50 text-purple-700'
                            : darkMode
                              ? 'text-white hover:bg-gray-600'
                              : 'text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        <span>{bytes} bytes</span>
                        {formData.algorithm === 'RSA' && (
                          <span className="text-xs opacity-70">
                            {bytes * 8} bits
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {validationError ? (
                <p className="text-xs font-light tracking-wide text-red-500 mt-2 flex items-center gap-1">
                  <span className="text-red-500">⚠️</span>
                  {validationError}
                </p>
              ) : (
                <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2">
                  {t('keys.upload.selectAllowedValue', { algorithm: formData.algorithm })}
                </p>
              )}
            </div>

            {/* Campo de mensaje solo si es HSM */}
            {isHSMService && (
              <div className="space-y-3">
                <h4 className="text-sm sm:text-base font-light tracking-wide flex items-center gap-2 text-gray-900 dark:text-white">
                  <MessageSquare size={16} className="text-blue-500 sm:w-5 sm:h-5" />
                  {t('keys.upload.message')}
                </h4>
                <textarea
                  value={formData.message}
                  onChange={(e) => handleChange('message', e.target.value)}
                  placeholder={t('keys.upload.messagePlaceholder')}
                  rows={4}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  }`}
                />
                <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400">
                  {t('keys.upload.messageHelp')}
                </p>
              </div>
            )}

            {/* Mostrar checkbox exportable solo si NO es HSM */}
            {!isHSMService && (
              <div className={`flex items-center gap-3 p-3 rounded-lg border ${
                darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
              }`}>
                <input
                  type="checkbox"
                  id="exportable"
                  checked={formData.exportable}
                  onChange={(e) => handleChange('exportable', e.target.checked)}
                  disabled={isUploading}
                  className={`w-4 h-4 text-purple-600 bg-white border-gray-300 rounded focus:ring-2 focus:ring-purple-500 transition-colors duration-200`}
                />
                <label htmlFor="exportable" className={`text-sm font-light tracking-wide ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {t('keys.upload.exportable')}
                </label>
              </div>
            )}
          </div>
        </div>

        {/* Generación Automática */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-start gap-3">
            <Hash size={18} className="text-blue-500 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-base font-light tracking-wide text-blue-700 dark:text-blue-300 mb-2">
                {t('keys.upload.autoGeneration')}
              </h4>
              <p className="text-sm font-light tracking-wide text-blue-600 dark:text-blue-400">
                {t('keys.upload.autoGenerationDesc')}
              </p>
            </div>
          </div>
        </div>

        {/* Botones */}
        <form onSubmit={handleSubmit}>
          <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            {isUploading ? (
              <button
                disabled
                className="px-8 py-3 rounded-xl bg-blue-600 text-white opacity-50 cursor-not-allowed flex items-center justify-center gap-2 font-light tracking-wide shadow-lg"
              >
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('keys.upload.uploading')}
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={onClose}
                  className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="submit"
                  disabled={!formData.key_name || validationError || (isHSMService && !formData.message)}
                  className={`px-8 py-3 rounded-xl bg-blue-600 hover:bg-blue-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-2 ${
                    (!formData.key_name || validationError || (isHSMService && !formData.message)) ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <Upload size={16} />
                  {t('keys.upload.uploadButton')}
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </Modal>
  );
};

KeyUploadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onUpload: PropTypes.func.isRequired,
  darkMode: PropTypes.bool,
  selectedService: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ])
};

export default KeyUploadModal;
