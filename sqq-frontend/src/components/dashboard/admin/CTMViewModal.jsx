import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';
import { Server, Database } from 'lucide-react';

const CTMViewModal = ({ isOpen, onClose, ctm, darkMode }) => {
  const { t } = useLanguage();
  if (!ctm) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <Server size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.ctms.detailCtm')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.ctms.detailCtmDescription')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
    >
      <div className="space-y-3 sm:space-y-4">
        {/* Información del CTM */}
        <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Server size={16} className="text-orange-500 sm:w-5 sm:h-5" />
            Información del CTM
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.ctms.form.name')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {ctm.name || 'CTM Principal'}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.ctms.form.status')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                ctm.status === 'active'
                  ? darkMode
                    ? 'border-green-600 bg-green-900/20 text-green-400'
                    : 'border-green-300 bg-green-50 text-green-700'
                  : darkMode
                    ? 'border-red-600 bg-red-900/20 text-red-400'
                    : 'border-red-300 bg-red-50 text-red-700'
              }`}>
                {t('admin.ctms.status.' + ctm.status)}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.ctms.form.ipAddress')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {ctm.ipAddress || '*************'}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.ctms.connection.username')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {ctm.username || '<EMAIL>'}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.ctms.connection.password')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                ••••••••
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.ctms.connection.domain')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {ctm.domain || 'root'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
        >
          {t('common.close')}
        </button>
      </div>
    </Modal>
  );
};

CTMViewModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  ctm: PropTypes.object,
  darkMode: PropTypes.bool
};

export default CTMViewModal;
