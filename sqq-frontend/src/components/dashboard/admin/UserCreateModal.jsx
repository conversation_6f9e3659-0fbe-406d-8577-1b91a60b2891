import { useState, useRef, useEffect } from 'react';
import { Plus, Eye, EyeOff, User, Settings, Key, ChevronDown, Server } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, Button, FormField } from '../../common';
import { useLanguage } from '../../../hooks';
import { validationRules } from '../../../utils/validations';

/**
 * Modal para crear nuevo usuario
 */
const UserCreateModal = ({ isOpen, onClose, onCreate, darkMode, ctms = [], hsms = [] }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    company: '',
    password: '',
    role: 'USER',
    selectedCtms: [],
    selectedHsms: [],
    seqrngConfigurations: [
      {
        id: 1,
        ipAddress: '',
        apiToken: ''
      }
    ]
  });
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [isServiceDropdownOpen, setIsServiceDropdownOpen] = useState(false);
  const serviceDropdownRef = useRef(null);
  const [isRoleDropdownOpen, setIsRoleDropdownOpen] = useState(false);
  const roleDropdownRef = useRef(null);
  
  // Simplificado: Solo una configuración por servicio, sin múltiples configuraciones

  // Cerrar dropdowns al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (serviceDropdownRef.current && !serviceDropdownRef.current.contains(event.target)) {
        setIsServiceDropdownOpen(false);
      }
      if (roleDropdownRef.current && !roleDropdownRef.current.contains(event.target)) {
        setIsRoleDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleSeqrngChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      seqrngConfigurations: [{
        ...prev.seqrngConfigurations[0],
        [field]: value
      }]
    }));
  };

  const handleCtmToggle = (ctmId) => {
    const currentSelected = formData.selectedCtms;
    const isSelected = currentSelected.includes(ctmId);

    if (isSelected) {
      handleChange('selectedCtms', currentSelected.filter(id => id !== ctmId));
    } else {
      handleChange('selectedCtms', [...currentSelected, ctmId]);
    }
  };

  const handleHsmToggle = (hsmId) => {
    const currentSelected = formData.selectedHsms;
    const isSelected = currentSelected.includes(hsmId);

    if (isSelected) {
      handleChange('selectedHsms', currentSelected.filter(id => id !== hsmId));
    } else {
      handleChange('selectedHsms', [...currentSelected, hsmId]);
    }
  };

  const handleRoleSelect = (roleValue) => {
    handleChange('role', roleValue);
    setIsRoleDropdownOpen(false);
  };

  // Solo trabajamos con una configuración fija para cada servicio
  const seqrngConfig = formData.seqrngConfigurations[0];

  const validateForm = () => {
    const newErrors = {};

    // Validar campos requeridos
    if (!formData.firstName) newErrors.firstName = t('admin.users.create.validation.firstNameRequired');
    if (!formData.lastName) newErrors.lastName = t('admin.users.create.validation.lastNameRequired');
    if (!formData.email) newErrors.email = t('admin.users.create.validation.emailRequired');
    if (!formData.password) newErrors.password = t('admin.users.create.validation.passwordRequired');

    // Validar contraseña con las nuevas reglas
    if (formData.password) {
      const passwordError = validationRules.password(formData.password);
      if (passwordError) {
        newErrors.password = passwordError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      // Convertir arrays a formato compatible con el backend actual
      const backendData = {
        ...formData,
        seqrngIpAddress: formData.seqrngConfigurations[0]?.ipAddress || '',
        seqrngApiToken: formData.seqrngConfigurations[0]?.apiToken || '',
        ctmIds: formData.selectedCtms,
        hsmIds: formData.selectedHsms
      };

      await onCreate(backendData);
      // Reset form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        company: '',
        password: '',
        role: 'USER',
        selectedCtms: [],
        selectedHsms: [],
        seqrngConfigurations: [{ id: 1, ipAddress: '', apiToken: '' }]
      });
      setErrors({});
      setShowPassword(false);
    } finally {
      setIsSaving(false);
    }
  };

  const roleOptions = [
    { value: 'USER', label: t('admin.users.create.roles.user') },
    { value: 'ADMIN', label: t('admin.users.create.roles.admin') }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <User size={24} className="text-green-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.users.create.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.users.create.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
      className="max-h-[90vh] overflow-y-auto"
    >
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Información Personal */}
          <div className="space-y-4 p-6 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-gray-50/50 dark:bg-gray-700/30 shadow-sm">
            <h4 className="text-lg font-light tracking-wide leading-relaxed border-b-2 border-blue-200 dark:border-blue-700 pb-3 mb-4 text-gray-800 dark:text-gray-200 flex items-center gap-3">
              <User size={22} className="text-blue-500" />
              {t('admin.users.create.personalInfo')}
            </h4>

            <FormField
              label={t('admin.users.create.firstName')}
              type="text"
              value={formData.firstName}
              onChange={(e) => handleChange('firstName', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.create.lastName')}
              type="text"
              value={formData.lastName}
              onChange={(e) => handleChange('lastName', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.users.create.email')}
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              required
              darkMode={darkMode}
            />

            <div className="relative">
              <FormField
                label={t('admin.users.create.password')}
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={(e) => handleChange('password', e.target.value)}
                required
                disabled={isSaving}
                darkMode={darkMode}
                className="pr-10"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                disabled={isSaving}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}
            </div>

            {/* Información sobre requisitos de contraseña */}
            <div className={`p-3 rounded-lg border ${
              darkMode ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
            }`}>
              <h6 className="font-medium text-blue-800 dark:text-blue-300 mb-1 text-sm">
                📋 {t('admin.users.create.passwordRequirements')}
              </h6>
              <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                <li>• {t('admin.users.create.minLength')}</li>
                <li>• {t('admin.users.create.alphanumeric')}</li>
              </ul>
            </div>

            <FormField
              label={t('admin.users.create.company')}
              type="text"
              value={formData.company}
              onChange={(e) => handleChange('company', e.target.value)}
              darkMode={darkMode}
            />

            <div className="relative" ref={roleDropdownRef}>
              <label className={`block text-sm font-light tracking-wide mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {t('admin.users.create.role')}
              </label>
              
              {/* Botón del dropdown de rol */}
              <button
                type="button"
                onClick={() => setIsRoleDropdownOpen(!isRoleDropdownOpen)}
                className={`w-full flex items-center justify-between px-4 py-3 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                  darkMode
                    ? 'bg-gray-800 border-gray-600 text-white hover:border-gray-500'
                    : 'bg-white border-gray-300 text-gray-900 hover:border-gray-400'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-1.5 rounded-lg ${
                    darkMode ? 'bg-purple-600' : 'bg-purple-500'
                  }`}>
                    <User size={16} className="text-white" />
                  </div>
                  <span className="font-light tracking-wide">
                    {formData.role === 'ADMIN' ? t('admin.users.create.roles.admin') : t('admin.users.create.roles.user')}
                  </span>
                </div>
                <ChevronDown 
                  size={16} 
                  className={`transition-transform duration-300 ${isRoleDropdownOpen ? 'rotate-180' : ''}`}
                />
              </button>

                                  {/* Dropdown personalizado de rol */}
                    {isRoleDropdownOpen && (
                      <div className={`absolute bottom-full left-0 right-0 mb-2 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
                        darkMode
                          ? 'bg-gray-800 border-gray-600'
                          : 'bg-white border-gray-200'
                      }`}>
                  {/* Header del dropdown */}
                  <div className={`px-4 py-3 border-b ${
                    darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                  }`}>
                    <div className="flex items-center gap-2">
                      <User size={16} className={darkMode ? 'text-purple-400' : 'text-purple-500'} />
                      <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                        {t('admin.users.create.role')}
                      </span>
                    </div>
                  </div>

                  {/* Lista de roles */}
                  <div className="py-2">
                    <button
                      type="button"
                      onClick={() => handleRoleSelect('USER')}
                      className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                        formData.role === 'USER'
                          ? darkMode
                            ? 'bg-purple-600/20 text-purple-400'
                            : 'bg-purple-50 text-purple-600'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-700'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex-1 text-left">
                        <span className="font-light tracking-wide">
                          {t('admin.users.create.roles.user')}
                        </span>
                        {formData.role === 'USER' && (
                          <div className="flex items-center gap-1 mt-1">
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-purple-400' : 'bg-purple-500'
                            }`}></div>
                            <span className="text-xs font-light tracking-wide opacity-75">
                              Actual
                            </span>
                          </div>
                        )}
                      </div>
                      {formData.role === 'USER' && (
                        <div className={`w-2 h-2 rounded-full ${
                          darkMode ? 'bg-purple-400' : 'bg-purple-500'
                        } animate-pulse`}></div>
                      )}
                    </button>

                    <button
                      type="button"
                      onClick={() => handleRoleSelect('ADMIN')}
                      className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                        formData.role === 'ADMIN'
                          ? darkMode
                            ? 'bg-purple-600/20 text-purple-400'
                            : 'bg-purple-50 text-purple-600'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-700'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex-1 text-left">
                        <span className="font-light tracking-wide">
                          {t('admin.users.create.roles.admin')}
                        </span>
                        {formData.role === 'ADMIN' && (
                          <div className="flex items-center gap-1 mt-1">
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-purple-400' : 'bg-purple-500'
                            }`}></div>
                            <span className="text-xs font-light tracking-wide opacity-75">
                              Actual
                            </span>
                          </div>
                        )}
                      </div>
                      {formData.role === 'ADMIN' && (
                        <div className={`w-2 h-2 rounded-full ${
                          darkMode ? 'bg-purple-400' : 'bg-purple-500'
                        } animate-pulse`}></div>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Configuración de Servicios */}
          <div className="space-y-4 p-6 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-gray-50/50 dark:bg-gray-700/30 shadow-sm">
            <h4 className="text-lg font-light tracking-wide leading-relaxed border-b-2 border-purple-200 dark:border-purple-700 pb-3 mb-4 text-gray-800 dark:text-gray-200 flex items-center gap-3">
              <Settings size={22} className="text-purple-500" />
              {t('admin.users.create.serviceConfig')}
            </h4>

            {/* Selección de Servicio (CTM o HSM) */}
            <div className="space-y-4">
              <h5 className="font-medium text-blue-600 dark:text-blue-400 flex items-center gap-2">
                <Settings size={18} className="text-blue-500" />
                {t('admin.users.create.serviceSelection')}
              </h5>

                             <div className={`p-4 rounded-xl border shadow-sm ${
                 darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
               }`}>
                 <div className="relative" ref={serviceDropdownRef}>
                   <label className={`block text-sm font-light tracking-wide mb-2 ${
                     darkMode ? 'text-gray-300' : 'text-gray-700'
                   }`}>
                     {t('admin.users.create.selectService')}
                   </label>
                   
                   {/* Botón del dropdown */}
                   <button
                     type="button"
                     onClick={() => setIsServiceDropdownOpen(!isServiceDropdownOpen)}
                     className={`w-full flex items-center justify-between px-4 py-3 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                       darkMode
                         ? 'bg-gray-800 border-gray-600 text-white hover:border-gray-500'
                         : 'bg-white border-gray-300 text-gray-900 hover:border-gray-400'
                     }`}
                   >
                     <div className="flex items-center gap-3">
                       <div className={`p-1.5 rounded-lg ${
                         darkMode ? 'bg-blue-600' : 'bg-blue-500'
                       }`}>
                         <Server size={16} className="text-white" />
                       </div>
                       <span className="font-light tracking-wide">
                         {(() => {
                           const totalSelected = formData.selectedCtms.length + formData.selectedHsms.length;
                           if (totalSelected === 0) {
                             return t('admin.users.create.selectServicePlaceholder');
                           } else if (totalSelected === 1) {
                             const selectedCtm = ctms.find(ctm => formData.selectedCtms.includes(ctm.id));
                             const selectedHsm = hsms.find(hsm => formData.selectedHsms.includes(hsm.id));
                             const selected = selectedCtm || selectedHsm;
                             return selected ? `${selected.name}` : 'Servicio seleccionado';
                           } else {
                             return `${totalSelected} servicios seleccionados`;
                           }
                         })()}
                       </span>
                     </div>
                     <ChevronDown 
                       size={16} 
                       className={`transition-transform duration-300 ${isServiceDropdownOpen ? 'rotate-180' : ''}`}
                     />
                   </button>

                                                                               {/* Dropdown personalizado */}
                     {isServiceDropdownOpen && (
                       <div className={`absolute top-full left-0 right-0 mt-2 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
                         darkMode
                           ? 'bg-gray-800 border-gray-600'
                           : 'bg-white border-gray-200'
                       }`}>
                       {/* Header del dropdown */}
                       <div className={`px-4 py-3 border-b ${
                         darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                       }`}>
                         <div className="flex items-center gap-2">
                           <Server size={16} className={darkMode ? 'text-blue-400' : 'text-blue-500'} />
                           <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                             {t('admin.users.create.selectService')}
                           </span>
                         </div>
                       </div>

                       {/* Lista de servicios */}
                       <div className="py-2 max-h-60 overflow-y-auto">
                         {/* Sección CTM */}
                         {ctms.length > 0 && (
                           <>
                             <div className={`px-4 py-2 text-xs font-medium ${
                               darkMode ? 'text-blue-400 bg-blue-900/20' : 'text-blue-600 bg-blue-50'
                             }`}>
                               CTM (Centralized Trust Management)
                             </div>
                             {ctms.map((ctm) => {
                               const isSelected = formData.selectedCtms.includes(ctm.id);
                               return (
                                 <button
                                   key={`ctm-${ctm.id}`}
                                   type="button"
                                   onClick={() => handleCtmToggle(ctm.id)}
                                   className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                                     isSelected
                                       ? darkMode
                                         ? 'bg-blue-600/20 text-blue-400'
                                         : 'bg-blue-50 text-blue-600'
                                       : darkMode
                                         ? 'text-gray-300 hover:bg-gray-700'
                                         : 'text-gray-700 hover:bg-gray-50'
                                   }`}
                                 >
                                   {/* Checkbox */}
                                   <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                                     isSelected
                                       ? darkMode
                                         ? 'bg-blue-600 border-blue-600'
                                         : 'bg-blue-500 border-blue-500'
                                       : darkMode
                                         ? 'border-gray-600'
                                         : 'border-gray-300'
                                   }`}>
                                     {isSelected && (
                                       <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                         <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                       </svg>
                                     )}
                                   </div>

                                   <div className="flex-1 text-left">
                                     <span className="font-light tracking-wide">
                                       {ctm.name}
                                     </span>
                                     <div className="text-xs opacity-75 mt-0.5">
                                       {ctm.ipAddress}
                                     </div>
                                   </div>
                                 </button>
                               );
                             })}
                           </>
                         )}

                         {/* Separador si hay ambos tipos */}
                         {ctms.length > 0 && hsms.length > 0 && (
                           <div className={`mx-4 my-2 border-t ${
                             darkMode ? 'border-gray-600' : 'border-gray-200'
                           }`}></div>
                         )}

                         {/* Sección HSM */}
                         {hsms.length > 0 && (
                           <>
                             <div className={`px-4 py-2 text-xs font-medium ${
                               darkMode ? 'text-emerald-400 bg-emerald-900/20' : 'text-emerald-600 bg-emerald-50'
                             }`}>
                               HSM (Hardware Security Module)
                             </div>
                             {hsms.map((hsm) => {
                               const isSelected = formData.selectedHsms.includes(hsm.id);
                               return (
                                 <button
                                   key={`hsm-${hsm.id}`}
                                   type="button"
                                   onClick={() => handleHsmToggle(hsm.id)}
                                   className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                                     isSelected
                                       ? darkMode
                                         ? 'bg-emerald-600/20 text-emerald-400'
                                         : 'bg-emerald-50 text-emerald-600'
                                       : darkMode
                                         ? 'text-gray-300 hover:bg-gray-700'
                                         : 'text-gray-700 hover:bg-gray-50'
                                   }`}
                                 >
                                   {/* Checkbox */}
                                   <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                                     isSelected
                                       ? darkMode
                                         ? 'bg-emerald-600 border-emerald-600'
                                         : 'bg-emerald-500 border-emerald-500'
                                       : darkMode
                                         ? 'border-gray-600'
                                         : 'border-gray-300'
                                   }`}>
                                     {isSelected && (
                                       <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                         <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                       </svg>
                                     )}
                                   </div>

                                   <div className="flex-1 text-left">
                                     <span className="font-light tracking-wide">
                                       {hsm.name}
                                     </span>
                                     <div className="text-xs opacity-75 mt-0.5">
                                       {hsm.url || hsm.ipAddress}
                                     </div>
                                   </div>
                                 </button>
                               );
                             })}
                           </>
                         )}
                       </div>
                     </div>
                   )}
                 </div>
               </div>
            </div>

            {/* Configuraciones de SEQRNG */}
            <div className="space-y-4">
              <h5 className="font-medium text-purple-600 dark:text-purple-400 flex items-center gap-2">
                <Key size={18} className="text-purple-500" />
                {t('admin.users.create.seqrng.title')}
              </h5>

              <div className={`p-4 rounded-lg border ${
                darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
              }`}>
                <div className="space-y-4">
                  <FormField
                    label={t('admin.users.create.seqrng.ipAddress')}
                    type="text"
                    value={seqrngConfig.ipAddress}
                    onChange={(e) => handleSeqrngChange('ipAddress', e.target.value)}
                    placeholder="https://seqrng.example.com:1982"
                    darkMode={darkMode}
                  />

                  <FormField
                    label={t('admin.users.create.seqrng.apiToken')}
                    type="text"
                    value={seqrngConfig.apiToken}
                    onChange={(e) => handleSeqrngChange('apiToken', e.target.value)}
                    placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
                    darkMode={darkMode}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer con botones centrados */}
        <div className="flex justify-center gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSaving}
            className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300 disabled:opacity-50"
          >
            {t('common.cancel')}
          </button>

          <button
            type="submit"
            disabled={isSaving}
            className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
          >
            <Plus size={16} className={isSaving ? "animate-spin" : ""} />
            <span>
              {isSaving ? t('admin.users.create.creating') : t('admin.users.create.createButton')}
            </span>
          </button>
        </div>
      </form>
    </Modal>
  );
};

UserCreateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreate: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired,
  ctms: PropTypes.array,
  hsms: PropTypes.array
};

export default UserCreateModal;
