import { useState, useEffect, useRef } from 'react';
import { Server, Save, ChevronDown, Shield } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { Modal, FormField } from '../../common';
import CTMConfigurationSection from './CTMConfigurationSection';

/**
 * Modal para editar un CTM existente
 * Diseño simplificado con campos de configuración CTM + nombre y estado
 */
const CTMEditModal = ({ isOpen, onClose, onSubmit, ctm, darkMode = false }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    status: 'active',
    config: {
      ipAddress: '',
      username: '',
      password: '',
      domain: ''
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const statusDropdownRef = useRef(null);

  // Cargar datos del CTM al abrir
  useEffect(() => {
    if (ctm) {
      setFormData({
        name: ctm.name || '',
        status: ctm.status || 'active',
        config: {
          ipAddress: ctm.ipAddress || ctm?.ctmConfig?.ipAddress || '',
          username: ctm.username || ctm?.ctmConfig?.username || '',
          password: ctm.password || ctm?.ctmConfig?.password || '',
          domain: ctm.domain || ctm?.ctmConfig?.domain || ''
        }
      });
    }
  }, [ctm]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Enviamos todos los campos: nombre, estado y configuración
      await onSubmit({
        name: formData.name,
        status: formData.status,
        ipAddress: formData.config.ipAddress,
        username: formData.config.username,
        password: formData.config.password,
        domain: formData.config.domain
      });
    } catch (error) {
      console.error('Error updating CTM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleConfigChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [field]: value
      }
    }));
  };

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleStatusSelect = (statusValue) => {
    handleChange('status', statusValue);
    setIsStatusDropdownOpen(false);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <Server size={24} className="text-blue-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              CipherTrust Manager
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.ctms.editModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Información básica del CTM */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide text-blue-700 dark:text-blue-300 mb-4 flex items-center gap-2">
            <Server size={20} className="text-blue-600" />
            {t('admin.ctms.editModal.configTitle')}
          </h4>

                     <div className="space-y-4">
             <div>
               <label className={`block text-sm font-light tracking-wide mb-2 ${
                 darkMode ? 'text-gray-300' : 'text-gray-700'
               }`}>
                 {t('admin.ctms.form.name')} <span className="text-red-500">*</span>
               </label>
                               <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder={t('admin.ctms.form.namePlaceholder')}
                  required
                  disabled={isSubmitting}
                  className={`w-full px-4 py-2.5 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                  } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
             </div>

             <div className="relative" ref={statusDropdownRef}>
               <label className={`block text-sm font-light tracking-wide mb-2 ${
                 darkMode ? 'text-gray-300' : 'text-gray-700'
               }`}>
                 {t('admin.ctms.form.status')}
               </label>
              
              {/* Botón del dropdown de estado */}
                             <button
                 type="button"
                 onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                 disabled={isSubmitting}
                 className={`w-full flex items-center justify-between px-4 py-3 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                   darkMode
                     ? 'bg-gray-800 border-gray-600 text-white hover:border-gray-500'
                     : 'bg-white border-gray-300 text-gray-900 hover:border-gray-400'
                 } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
               >
                <div className="flex items-center gap-3">
                  <div className={`p-1.5 rounded-lg ${
                    formData.status === 'active'
                      ? (darkMode ? 'bg-green-600' : 'bg-green-500')
                      : (darkMode ? 'bg-red-600' : 'bg-red-500')
                  }`}>
                    <Shield size={16} className="text-white" />
                  </div>
                  <span className="font-light tracking-wide">
                    {formData.status === 'active' ? t('admin.ctms.status.active') : t('admin.ctms.status.inactive')}
                  </span>
                </div>
                <ChevronDown 
                  size={16} 
                  className={`transition-transform duration-300 ${isStatusDropdownOpen ? 'rotate-180' : ''}`}
                />
              </button>

              {/* Dropdown personalizado de estado */}
              {isStatusDropdownOpen && (
                <div className={`absolute top-full left-0 right-0 mt-2 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
                  darkMode
                    ? 'bg-gray-800 border-gray-600'
                    : 'bg-white border-gray-200'
                }`}>
                  {/* Header del dropdown */}
                  <div className={`px-4 py-3 border-b ${
                    darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                  }`}>
                    <div className="flex items-center gap-2">
                      <Shield size={16} className={darkMode ? 'text-green-400' : 'text-green-500'} />
                      <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                        {t('admin.ctms.form.status')}
                      </span>
                    </div>
                  </div>

                  {/* Lista de estados */}
                  <div className="py-2">
                    <button
                      type="button"
                      onClick={() => handleStatusSelect('active')}
                      className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                        formData.status === 'active'
                          ? darkMode
                            ? 'bg-green-600/20 text-green-400'
                            : 'bg-green-50 text-green-600'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-700'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex-1 text-left">
                        <span className="font-light tracking-wide">
                          {t('admin.ctms.status.active')}
                        </span>
                        {formData.status === 'active' && (
                          <div className="flex items-center gap-1 mt-1">
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-green-400' : 'bg-green-500'
                            }`}></div>
                            <span className="text-xs font-light tracking-wide opacity-75">
                              Actual
                            </span>
                          </div>
                        )}
                      </div>
                      {formData.status === 'active' && (
                        <div className={`w-2 h-2 rounded-full ${
                          darkMode ? 'bg-green-400' : 'bg-green-500'
                        } animate-pulse`}></div>
                      )}
                    </button>

                    <button
                      type="button"
                      onClick={() => handleStatusSelect('inactive')}
                      className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                        formData.status === 'inactive'
                          ? darkMode
                            ? 'bg-red-600/20 text-red-400'
                            : 'bg-red-50 text-red-600'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-700'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex-1 text-left">
                        <span className="font-light tracking-wide">
                          {t('admin.ctms.status.inactive')}
                        </span>
                        {formData.status === 'inactive' && (
                          <div className="flex items-center gap-1 mt-1">
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-red-400' : 'bg-red-500'
                            }`}></div>
                            <span className="text-xs font-light tracking-wide opacity-75">
                              Actual
                            </span>
                          </div>
                        )}
                      </div>
                      {formData.status === 'inactive' && (
                        <div className={`w-2 h-2 rounded-full ${
                          darkMode ? 'bg-red-400' : 'bg-red-500'
                        } animate-pulse`}></div>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Configuración de conexión CTM */}
        <CTMConfigurationSection
          config={formData.config}
          onConfigChange={handleConfigChange}
          darkMode={darkMode}
          required={true}
        />

        {/* Footer - estilo consistente con editar HSM */}
        <div className="flex justify-center gap-3 mt-2 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
            } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white flex items-center justify-center gap-2 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('common.loading')}
              </>
            ) : (
              <>
                <Save size={16} />
                {t('admin.ctms.editModal.submit')}
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

CTMEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  ctm: PropTypes.object.isRequired,
  darkMode: PropTypes.bool
};

export default CTMEditModal;
