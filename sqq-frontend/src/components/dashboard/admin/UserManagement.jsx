import { useState } from 'react';
import { Plus, Edit, Trash2 } from 'lucide-react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON> } from '../../common';
import { useLanguage } from '../../../hooks';
import UserCard from './UserCard';
import UserEditModal from './UserEditModal';
import UserCreateModal from './UserCreateModal';
import UserDeleteModal from './UserDeleteModal';
import UserDetailModal from './UserDetailModal';



/**
 * Componente para gestión de usuarios en el AdminDashboard
 */
const UserManagement = ({
  users,
  isLoading,
  error,
  onClearError,
  onUpdateUser,
  onCreateUser,
  onDeleteUser,
  darkMode,
  ctms = [],
  hsms = []
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [detailUser, setDetailUser] = useState(null);

  const { t } = useLanguage();

  const handleEditUser = (user) => {
    setEditingUser({ ...user });
    setShowEditModal(true);
  };



  const handleViewDetail = (user) => {
    setDetailUser(user);
    setShowDetailModal(true);
  };

  const handleDeleteUser = (user) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  const handleSaveUser = async (userData) => {
    await onUpdateUser(editingUser.id, userData);
    setShowEditModal(false);
    setEditingUser(null);
  };

  const handleCreateUser = async (userData) => {
    await onCreateUser(userData);
    setShowCreateModal(false);
  };

  const handleConfirmDelete = async () => {
    try {
      await onDeleteUser(userToDelete.id);
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (error) {
      console.error('Error eliminando usuario:', error);
      // TODO: Mostrar notificación de error al usuario
    }
  };



  return (
    <>
      {/* Header con botón en la esquina superior derecha */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-light tracking-wide leading-relaxed mb-2 text-gray-900 dark:text-white">
              {t('admin.users.title')}
            </h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 font-light tracking-wide">
              {t('admin.users.subtitle')}
            </p>
          </div>

          <div className="flex justify-center sm:justify-end">
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm sm:text-base"
            >
              <Plus size={16} />
              <span>{t('admin.users.createUser')}</span>
            </button>
          </div>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {isLoading ? (
        <LoadingSpinner message="Cargando usuarios y estadísticas..." />
      ) : (
        <div className="max-h-[600px] overflow-y-auto space-y-3 pr-4">
          {users.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400 font-light tracking-wide">
              No hay usuarios registrados
            </div>
          ) : (
            users.map((user) => (
              <UserCard
                key={user.id}
                user={user}
                darkMode={darkMode}
                onEdit={() => handleEditUser(user)}
                onViewDetail={() => handleViewDetail(user)}
                onDelete={() => handleDeleteUser(user)}
              />
            ))
          )}
        </div>
      )}

      {/* Modales */}
      <UserEditModal
        isOpen={showEditModal}
        user={editingUser}
        onClose={() => setShowEditModal(false)}
        onSave={handleSaveUser}
        darkMode={darkMode}
      />

      <UserCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreate={handleCreateUser}
        darkMode={darkMode}
        ctms={ctms}
        hsms={hsms}
      />

      <UserDeleteModal
        isOpen={showDeleteModal}
        user={userToDelete}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleConfirmDelete}
        darkMode={darkMode}
      />

      <UserDetailModal
        isOpen={showDetailModal}
        user={detailUser}
        onClose={() => setShowDetailModal(false)}
        darkMode={darkMode}
      />



    </>
  );
};

UserManagement.propTypes = {
  users: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateUser: PropTypes.func.isRequired,
  onCreateUser: PropTypes.func.isRequired,
  onDeleteUser: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired,
  ctms: PropTypes.array,
  hsms: PropTypes.array
};

export default UserManagement;
