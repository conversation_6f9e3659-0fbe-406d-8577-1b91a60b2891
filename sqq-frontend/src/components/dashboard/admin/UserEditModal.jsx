import { useState, useEffect, useRef } from 'react';
import { Save, User, Database, Key, ChevronDown, Shield } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal para editar información de usuario
 */
const UserEditModal = ({ isOpen, user, onClose, onSave, darkMode }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    role: 'USER',
    isActive: true,
    ctmConfigurations: [
      {
        id: 1,
        ipAddress: '',
        username: '',
        password: '',
        domain: ''
      }
    ],
    seqrngConfigurations: [
      {
        id: 1,
        ipAddress: '',
        apiToken: ''
      }
    ]
  });
  const [isSaving, setIsSaving] = useState(false);
  const [isRoleDropdownOpen, setIsRoleDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const roleDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);
  
  // Simplificado: Solo una configuración por servicio, sin múltiples configuraciones

  // Cerrar dropdowns al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (roleDropdownRef.current && !roleDropdownRef.current.contains(event.target)) {
        setIsRoleDropdownOpen(false);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        company: user.company || '',
        role: user.role || 'USER',
        isActive: user.isActive !== undefined ? user.isActive : true,
        ctmConfigurations: [
          {
            id: 1,
            ipAddress: user.ctmIpAddress || '',
            username: user.ctmUsername || '',
            password: user.ctmPassword || '',
            domain: user.ctmDomain || ''
          }
        ],
        seqrngConfigurations: [
          {
            id: 1,
            ipAddress: user.seqrngIpAddress || '',
            apiToken: user.seqrngApiToken || ''
          }
        ]
      });
    }
  }, [user]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCtmChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      ctmConfigurations: [{
        ...prev.ctmConfigurations[0],
        [field]: value
      }]
    }));
  };

  const handleSeqrngChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      seqrngConfigurations: [{
        ...prev.seqrngConfigurations[0],
        [field]: value
      }]
    }));
  };

  const handleRoleSelect = (roleValue) => {
    handleChange('role', roleValue);
    setIsRoleDropdownOpen(false);
  };

  const handleStatusSelect = (statusValue) => {
    handleChange('isActive', statusValue);
    setIsStatusDropdownOpen(false);
  };

  // Solo trabajamos con una configuración fija para cada servicio
  const ctmConfig = formData.ctmConfigurations[0];
  const seqrngConfig = formData.seqrngConfigurations[0];

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    try {
      // Convertir arrays a formato compatible con el backend actual
      const backendData = {
        ...formData,
        ctmIpAddress: formData.ctmConfigurations[0]?.ipAddress || '',
        ctmUsername: formData.ctmConfigurations[0]?.username || '',
        ctmPassword: formData.ctmConfigurations[0]?.password || '',
        ctmDomain: formData.ctmConfigurations[0]?.domain || '',
        seqrngIpAddress: formData.seqrngConfigurations[0]?.ipAddress || '',
        seqrngApiToken: formData.seqrngConfigurations[0]?.apiToken || ''
      };

      await onSave(backendData);
    } finally {
      setIsSaving(false);
    }
  };

  const roleOptions = [
    { value: 'USER', label: t('admin.users.edit.roles.user') },
    { value: 'ADMIN', label: t('admin.users.edit.roles.admin') }
  ];

  const statusOptions = [
    { value: true, label: t('admin.users.edit.statusOptions.active') },
    { value: false, label: t('admin.users.edit.statusOptions.inactive') }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <User size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.users.edit.title')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.users.edit.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-3 sm:space-y-4">
          {/* Información Personal */}
          <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
              <User size={16} className="text-orange-500 sm:w-5 sm:h-5" />
              {t('admin.users.edit.personalInfo')}
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.firstName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleChange('firstName', e.target.value)}
                  required
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.lastName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleChange('lastName', e.target.value)}
                  required
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.email')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  required
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.company')}
                </label>
                <input
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleChange('company', e.target.value)}
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div className="relative" ref={roleDropdownRef}>
                <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.role')}
                </label>
                
                {/* Botón del dropdown de rol */}
                <button
                  type="button"
                  onClick={() => setIsRoleDropdownOpen(!isRoleDropdownOpen)}
                  disabled={isSaving}
                  className={`w-full flex items-center justify-between px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white hover:border-gray-500'
                      : 'bg-white border-gray-300 text-gray-900 hover:border-gray-400'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-1.5 rounded-lg ${
                      darkMode ? 'bg-purple-600' : 'bg-purple-500'
                    }`}>
                      <User size={16} className="text-white" />
                    </div>
                    <span className="font-light tracking-wide">
                      {formData.role === 'ADMIN' ? t('admin.users.edit.roles.admin') : t('admin.users.edit.roles.user')}
                    </span>
                  </div>
                  <ChevronDown 
                    size={16} 
                    className={`transition-transform duration-300 ${isRoleDropdownOpen ? 'rotate-180' : ''}`}
                  />
                </button>

                {/* Dropdown personalizado de rol */}
                {isRoleDropdownOpen && (
                  <div className={`absolute top-full left-0 right-0 mt-2 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600'
                      : 'bg-white border-gray-200'
                  }`}>
                    {/* Header del dropdown */}
                    <div className={`px-4 py-3 border-b ${
                      darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                    }`}>
                      <div className="flex items-center gap-2">
                        <User size={16} className={darkMode ? 'text-purple-400' : 'text-purple-500'} />
                        <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                          {t('admin.users.edit.role')}
                        </span>
                      </div>
                    </div>

                    {/* Lista de roles */}
                    <div className="py-2">
                      <button
                        type="button"
                        onClick={() => handleRoleSelect('USER')}
                        className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                          formData.role === 'USER'
                            ? darkMode
                              ? 'bg-purple-600/20 text-purple-400'
                              : 'bg-purple-50 text-purple-600'
                            : darkMode
                              ? 'text-gray-300 hover:bg-gray-700'
                              : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex-1 text-left">
                          <span className="font-light tracking-wide">
                            {t('admin.users.edit.roles.user')}
                          </span>
                          {formData.role === 'USER' && (
                            <div className="flex items-center gap-1 mt-1">
                              <div className={`w-2 h-2 rounded-full ${
                                darkMode ? 'bg-purple-400' : 'bg-purple-500'
                              }`}></div>
                              <span className="text-xs font-light tracking-wide opacity-75">
                                Actual
                              </span>
                            </div>
                          )}
                        </div>
                        {formData.role === 'USER' && (
                          <div className={`w-2 h-2 rounded-full ${
                            darkMode ? 'bg-purple-400' : 'bg-purple-500'
                          } animate-pulse`}></div>
                        )}
                      </button>

                      <button
                        type="button"
                        onClick={() => handleRoleSelect('ADMIN')}
                        className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                          formData.role === 'ADMIN'
                            ? darkMode
                              ? 'bg-purple-600/20 text-purple-400'
                              : 'bg-purple-50 text-purple-600'
                            : darkMode
                              ? 'text-gray-300 hover:bg-gray-700'
                              : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex-1 text-left">
                          <span className="font-light tracking-wide">
                            {t('admin.users.edit.roles.admin')}
                          </span>
                          {formData.role === 'ADMIN' && (
                            <div className="flex items-center gap-1 mt-1">
                              <div className={`w-2 h-2 rounded-full ${
                                darkMode ? 'bg-purple-400' : 'bg-purple-500'
                              }`}></div>
                              <span className="text-xs font-light tracking-wide opacity-75">
                                Actual
                              </span>
                            </div>
                          )}
                        </div>
                        {formData.role === 'ADMIN' && (
                          <div className={`w-2 h-2 rounded-full ${
                            darkMode ? 'bg-purple-400' : 'bg-purple-500'
                          } animate-pulse`}></div>
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="relative" ref={statusDropdownRef}>
                <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.status')}
                </label>
                
                {/* Botón del dropdown de estado */}
                <button
                  type="button"
                  onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                  disabled={isSaving}
                  className={`w-full flex items-center justify-between px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white hover:border-gray-500'
                      : 'bg-white border-gray-300 text-gray-900 hover:border-gray-400'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-1.5 rounded-lg ${
                      formData.isActive 
                        ? (darkMode ? 'bg-green-600' : 'bg-green-500')
                        : (darkMode ? 'bg-red-600' : 'bg-red-500')
                    }`}>
                      <Shield size={16} className="text-white" />
                    </div>
                    <span className="font-light tracking-wide">
                      {formData.isActive ? t('admin.users.edit.statusOptions.active') : t('admin.users.edit.statusOptions.inactive')}
                    </span>
                  </div>
                  <ChevronDown 
                    size={16} 
                    className={`transition-transform duration-300 ${isStatusDropdownOpen ? 'rotate-180' : ''}`}
                  />
                </button>

                {/* Dropdown personalizado de estado */}
                {isStatusDropdownOpen && (
                  <div className={`absolute top-full left-0 right-0 mt-2 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600'
                      : 'bg-white border-gray-200'
                  }`}>
                    {/* Header del dropdown */}
                    <div className={`px-4 py-3 border-b ${
                      darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                    }`}>
                      <div className="flex items-center gap-2">
                        <Shield size={16} className={darkMode ? 'text-green-400' : 'text-green-500'} />
                        <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                          {t('admin.users.edit.status')}
                        </span>
                      </div>
                    </div>

                    {/* Lista de estados */}
                    <div className="py-2">
                      <button
                        type="button"
                        onClick={() => handleStatusSelect(true)}
                        className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                          formData.isActive
                            ? darkMode
                              ? 'bg-green-600/20 text-green-400'
                              : 'bg-green-50 text-green-600'
                            : darkMode
                              ? 'text-gray-300 hover:bg-gray-700'
                              : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex-1 text-left">
                          <span className="font-light tracking-wide">
                            {t('admin.users.edit.statusOptions.active')}
                          </span>
                          {formData.isActive && (
                            <div className="flex items-center gap-1 mt-1">
                              <div className={`w-2 h-2 rounded-full ${
                                darkMode ? 'bg-green-400' : 'bg-green-500'
                              }`}></div>
                              <span className="text-xs font-light tracking-wide opacity-75">
                                Actual
                              </span>
                            </div>
                          )}
                        </div>
                        {formData.isActive && (
                          <div className={`w-2 h-2 rounded-full ${
                            darkMode ? 'bg-green-400' : 'bg-green-500'
                          } animate-pulse`}></div>
                        )}
                      </button>

                      <button
                        type="button"
                        onClick={() => handleStatusSelect(false)}
                        className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                          !formData.isActive
                            ? darkMode
                              ? 'bg-red-600/20 text-red-400'
                              : 'bg-red-50 text-red-600'
                            : darkMode
                              ? 'text-gray-300 hover:bg-gray-700'
                              : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex-1 text-left">
                          <span className="font-light tracking-wide">
                            {t('admin.users.edit.statusOptions.inactive')}
                          </span>
                          {!formData.isActive && (
                            <div className="flex items-center gap-1 mt-1">
                              <div className={`w-2 h-2 rounded-full ${
                                darkMode ? 'bg-red-400' : 'bg-red-500'
                              }`}></div>
                              <span className="text-xs font-light tracking-wide opacity-75">
                                Actual
                              </span>
                            </div>
                          )}
                        </div>
                        {!formData.isActive && (
                          <div className={`w-2 h-2 rounded-full ${
                            darkMode ? 'bg-red-400' : 'bg-red-500'
                          } animate-pulse`}></div>
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Configuración CTM */}
          <div className={`p-4 rounded-xl border shadow-sm ${
            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <h4 className="text-base font-light tracking-wide flex items-center gap-2 text-gray-900 dark:text-white mb-4">
              <Database size={18} className="text-blue-500" />
              {t('admin.users.edit.ctm.title')}
            </h4>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.ctm.ipAddress')}
                </label>
                <input
                  type="text"
                  value={ctmConfig.ipAddress}
                  onChange={(e) => handleCtmChange('ipAddress', e.target.value)}
                  placeholder="https://ctm.example.com:443"
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.ctm.username')}
                </label>
                <input
                  type="text"
                  value={ctmConfig.username}
                  onChange={(e) => handleCtmChange('username', e.target.value)}
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.ctm.password')}
                </label>
                <input
                  type="password"
                  value={ctmConfig.password}
                  onChange={(e) => handleCtmChange('password', e.target.value)}
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.ctm.domain')}
                </label>
                <input
                  type="text"
                  value={ctmConfig.domain}
                  onChange={(e) => handleCtmChange('domain', e.target.value)}
                  placeholder="root"
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>
            </div>
          </div>

          {/* Configuración SEQRNG */}
          <div className={`p-4 rounded-xl border shadow-sm ${
            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <h4 className="text-base font-light tracking-wide flex items-center gap-2 text-gray-900 dark:text-white mb-4">
              <Key size={18} className="text-green-500" />
              {t('admin.users.edit.seqrng.title')}
            </h4>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.seqrng.ipAddress')}
                </label>
                <input
                  type="text"
                  value={seqrngConfig.ipAddress}
                  onChange={(e) => handleSeqrngChange('ipAddress', e.target.value)}
                  placeholder="https://seqrng.example.com:1982"
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>

              <div>
                <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                  {t('admin.users.edit.seqrng.apiToken')}
                </label>
                <input
                  type="text"
                  value={seqrngConfig.apiToken}
                  onChange={(e) => handleSeqrngChange('apiToken', e.target.value)}
                  placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
                  disabled={isSaving}
                  className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 text-sm sm:text-base ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                      : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer fijo centrado */}
        <div className="flex justify-center gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSaving}
            className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300 disabled:opacity-50"
          >
            {t('common.cancel')}
          </button>

          <button
            type="submit"
            disabled={isSaving}
            className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Save size={16} className={isSaving ? "animate-spin" : ""} />
            </div>
            <span>
              {isSaving ? t('admin.users.edit.saving') : t('admin.users.edit.saveButton')}
            </span>
          </button>
        </div>
      </form>
    </Modal>
  );
};

UserEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  user: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default UserEditModal;
