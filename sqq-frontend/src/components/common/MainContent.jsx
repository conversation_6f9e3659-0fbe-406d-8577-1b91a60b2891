import { useRef, useEffect, useState } from 'react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import { Menu, X, Info } from 'lucide-react';
import LanguageSelector from './LanguageSelector';
import ServiceSelector from './ServiceSelector';
import GlobalServiceSelector from './GlobalServiceSelector';
import Sidebar from './Sidebar';
import { useLanguage } from '../../hooks';

/**
 * Componente MainContent reutilizable
 * Proporciona el contenedor principal con animaciones consistentes
 */
const MainContent = ({
  children,
  darkMode,
  className = "",
  // Props para móvil
  title,
  currentUser,
  onToggleDarkMode,
  onLogout,
  navigationItems,
  activeSection,
  onSectionChange,
  // Props para selector de servicios unificado
  selectedService,
  onServiceChange,
  availableServices,
  // Prop para distinguir entre Admin y Usuario
  isAdminPage = false
}) => {
  const mainContentRef = useRef(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showServiceTooltip, setShowServiceTooltip] = useState(false);
  const tooltipRef = useRef(null);
  const { t } = useLanguage();

  // Animaciones GSAP al montar
  useEffect(() => {
    gsap.fromTo(
      mainContentRef.current,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out", delay: 0.3 }
    );
  }, []);

  // Cerrar tooltip al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
        setShowServiceTooltip(false);
      }
    };

    if (showServiceTooltip) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showServiceTooltip]);

  return (
    <>
      <main
        ref={mainContentRef}
        className={`flex-1 p-3 sm:p-6 lg:p-10 overflow-y-auto relative transition-colors duration-500
          ${darkMode ? "bg-gray-700" : "bg-gray-50"} ${className}`}
      >
        {/* Header móvil con menú hamburguesa */}
        <div className="flex justify-between items-center mb-4 sm:mb-6 lg:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className={`p-2 rounded-lg transition-colors ${
              darkMode
                ? 'hover:bg-gray-600 text-white'
                : 'hover:bg-gray-200 text-gray-900'
            }`}
          >
            <Menu size={24} />
          </button>
          <LanguageSelector darkMode={darkMode} />
        </div>

        {/* Header desktop con selector de servicios (izquierda) y selector de idioma (derecha) */}
        <div className="hidden lg:flex justify-between items-center mb-6">
          {/* Selector de servicios global - IZQUIERDA - Solo visible para usuarios (no admin) */}
          <div className="flex items-center gap-3">
            {!isAdminPage && (
              <>
                <GlobalServiceSelector
                  darkMode={darkMode}
                />
                
                {/* Tooltip informativo */}
                <div className="relative" ref={tooltipRef}>
                  <button
                    onMouseEnter={() => setShowServiceTooltip(true)}
                    onMouseLeave={() => setShowServiceTooltip(false)}
                    onClick={() => setShowServiceTooltip(!showServiceTooltip)}
                    className={`p-2 rounded-full transition-all duration-200 hover:scale-110 ${
                      darkMode
                        ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-600'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
                    }`}
                    title="Información sobre el selector de servicios"
                  >
                    <Info size={16} />
                  </button>

                  {/* Tooltip */}
                  {showServiceTooltip && (
                    <div className={`absolute top-full left-0 mt-2 w-64 p-3 rounded-lg border shadow-lg z-50 transition-all duration-200 ${
                      darkMode
                        ? 'bg-gray-800 border-gray-600 text-gray-200'
                        : 'bg-white border-gray-200 text-gray-700'
                    }`}>
                      <div className="flex items-start gap-2">
                        <div className={`p-1 rounded-full ${
                          darkMode ? 'bg-blue-600' : 'bg-blue-500'
                        }`}>
                          <Info size={12} className="text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium mb-1">
                            {t('common.tooltips.serviceSelector.title')}
                          </p>
                          <p className="text-xs leading-relaxed opacity-90">
                            {t('common.tooltips.serviceSelector.content')}
                          </p>
                        </div>
                      </div>
                      
                      {/* Flecha del tooltip */}
                      <div className={`absolute -top-1 left-4 w-2 h-2 rotate-45 ${
                        darkMode ? 'bg-gray-800 border-l border-t border-gray-600' : 'bg-white border-l border-t border-gray-200'
                      }`}></div>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
          
          {/* Selector de idioma - DERECHA */}
          <LanguageSelector darkMode={darkMode} />
        </div>

        {/* Contenedor principal */}
        <div
          className={`relative z-10 transition-all duration-500 rounded-xl p-4 sm:p-6 lg:p-8 shadow-md border
            ${darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-200 text-gray-900"}`}
        >
          {children}
        </div>
      </main>

      {/* Sidebar móvil como overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setIsMobileMenuOpen(false)}
          />

          {/* Sidebar */}
          <div className="relative w-80 max-w-[85vw]">
            <Sidebar
              title={title}
              currentUser={currentUser}
              darkMode={darkMode}
              onToggleDarkMode={onToggleDarkMode}
              onLogout={onLogout}
              navigationItems={navigationItems}
              activeSection={activeSection}
              onSectionChange={(section) => {
                onSectionChange(section);
                setIsMobileMenuOpen(false);
              }}
            />

            {/* Botón cerrar móvil */}
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="absolute top-4 right-4 p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <X size={20} className="text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

MainContent.propTypes = {
  children: PropTypes.node.isRequired,
  darkMode: PropTypes.bool.isRequired,
  className: PropTypes.string,
  // Props opcionales para móvil
  title: PropTypes.string,
  currentUser: PropTypes.object,
  onToggleDarkMode: PropTypes.func,
  onLogout: PropTypes.func,
  navigationItems: PropTypes.array,
  activeSection: PropTypes.string,
  onSectionChange: PropTypes.func,
  // Props opcionales para selector de servicios unificado
  selectedService: PropTypes.string,
  onServiceChange: PropTypes.func,
  availableServices: PropTypes.array,
  // Prop para distinguir entre Admin y Usuario
  isAdminPage: PropTypes.bool
};

export default MainContent;
