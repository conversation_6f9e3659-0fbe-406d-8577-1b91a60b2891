/**
 * useServices Hook
 * Hook personalizado para manejo de servicios CTM y HSM
 */

import { useState, useCallback, useEffect } from 'react';
import { ctmService, hsmService } from '../services/index.js';

export const useServices = () => {
  const [ctms, setCtms] = useState([]);
  const [hsms, setHsms] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Obtener CTMs del usuario autenticado
   */
  const getUserCtms = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ctmService.getUserCtms();
      setCtms(response || []);

      return response;
    } catch (error) {
      setError(error.message);
      setCtms([]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener HSMs del usuario autenticado
   */
  const getUserHsms = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await hsmService.getUserHsms();
      setHsms(response || []);

      return response;
    } catch (error) {
      setError(error.message);
      setHsms([]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener todos los servicios del usuario (CTMs + HSMs)
   */
  const getUserServices = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('useServices: Getting user services...');

      const [ctmsResponse, hsmsResponse] = await Promise.all([
        ctmService.getUserCtms().catch((error) => {
          console.error('useServices: Error getting user CTMs:', error);
          return [];
        }),
        hsmService.getUserHsms().catch((error) => {
          console.error('useServices: Error getting user HSMs:', error);
          return [];
        })
      ]);

      console.log('useServices: CTMs response:', ctmsResponse);
      console.log('useServices: HSMs response:', hsmsResponse);

      setCtms(ctmsResponse || []);
      setHsms(hsmsResponse || []);

      return {
        ctms: ctmsResponse || [],
        hsms: hsmsResponse || []
      };
    } catch (error) {
      console.error('useServices: Error in getUserServices:', error);
      setError(error.message);
      setCtms([]);
      setHsms([]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener todos los CTMs (Solo Admin)
   */
  const getAllCtms = useCallback(async (filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ctmService.getAllCtms(filters);
      setCtms(response || []);

      return response;
    } catch (error) {
      setError(error.message);
      setCtms([]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener todos los HSMs (Solo Admin)
   */
  const getAllHsms = useCallback(async (filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await hsmService.getAllHsms(filters);
      setHsms(response || []);

      return response;
    } catch (error) {
      setError(error.message);
      setHsms([]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Crear CTM (Solo Admin)
   */
  const createCtm = useCallback(async (ctmData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ctmService.createCtm(ctmData);
      
      // Actualizar la lista de CTMs
      setCtms(prevCtms => [response, ...prevCtms]);

      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Crear HSM (Solo Admin)
   */
  const createHsm = useCallback(async (hsmData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await hsmService.createHsm(hsmData);
      
      // Actualizar la lista de HSMs
      setHsms(prevHsms => [response, ...prevHsms]);

      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Actualizar CTM (Solo Admin)
   */
  const updateCtm = useCallback(async (ctmId, ctmData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ctmService.updateCtm(ctmId, ctmData);
      
      // Actualizar la lista de CTMs
      setCtms(prevCtms => 
        prevCtms.map(ctm => ctm.id === ctmId ? response : ctm)
      );

      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Actualizar HSM (Solo Admin)
   */
  const updateHsm = useCallback(async (hsmId, hsmData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await hsmService.updateHsm(hsmId, hsmData);
      
      // Actualizar la lista de HSMs
      setHsms(prevHsms => 
        prevHsms.map(hsm => hsm.id === hsmId ? response : hsm)
      );

      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Eliminar CTM (Solo Admin)
   */
  const deleteCtm = useCallback(async (ctmId) => {
    try {
      setIsLoading(true);
      setError(null);

      await ctmService.deleteCtm(ctmId);
      
      // Remover de la lista de CTMs
      setCtms(prevCtms => prevCtms.filter(ctm => ctm.id !== ctmId));

      return true;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Eliminar HSM (Solo Admin)
   */
  const deleteHsm = useCallback(async (hsmId) => {
    try {
      setIsLoading(true);
      setError(null);

      await hsmService.deleteHsm(hsmId);
      
      // Remover de la lista de HSMs
      setHsms(prevHsms => prevHsms.filter(hsm => hsm.id !== hsmId));

      return true;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Formatear servicios para el ServiceSelector
   */
  const getFormattedServices = useCallback(() => {
    console.log('useServices: Formatting services...', { ctms, hsms });

    const formattedCtms = ctms.map(ctm => ({
      id: ctm.id,
      name: ctm.name,
      type: 'CTM',
      description: ctm.description || 'CipherTrust Manager',
      isActive: ctm.isActive,
      hasCompleteConfiguration: ctm.hasCompleteConfiguration
    }));

    const formattedHsms = hsms.map(hsm => ({
      id: hsm.id,
      name: hsm.name,
      type: 'HSM',
      description: hsm.description || 'Hardware Security Module',
      isActive: hsm.isActive,
      hasCompleteConfiguration: hsm.hasCompleteConfiguration
    }));

    const result = [...formattedCtms, ...formattedHsms];
    console.log('useServices: Formatted result:', result);
    return result;
  }, [ctms, hsms]);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Estados
    ctms,
    hsms,
    isLoading,
    error,
    
    // Funciones para usuarios
    getUserCtms,
    getUserHsms,
    getUserServices,
    
    // Funciones para admin
    getAllCtms,
    getAllHsms,
    createCtm,
    createHsm,
    updateCtm,
    updateHsm,
    deleteCtm,
    deleteHsm,
    
    // Utilidades
    getFormattedServices,
    clearError
  };
};
