/**
 * useKeys Hook
 * Hook personalizado para manejo de llaves de encriptación
 */

import { useState, useCallback } from 'react';
import { keyService, hsmKeyService } from '../services/index.js';

export const useKeys = () => {
  const [keys, setKeys] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Estados para paginación del servidor
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalKeys, setTotalKeys] = useState(0);
  const [keysPerPage] = useState(100); // Máximo permitido por el backend

  /**
   * Subir llave a CTM
   */
  const uploadToCTM = useCallback(async (keyData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await keyService.uploadToCTM(keyData);

      // Preservar el algoritmo original del frontend si el backend no lo devuelve correctamente
      const enhancedResponse = {
        ...response,
        algorithm: keyData.algorithm || response.algorithm,
        // Asegurar que el algoritmo esté disponible en ambos campos
        type: keyData.algorithm || response.algorithm || response.type
      };

      // Agregar nueva llave a la lista (siempre)
      setKeys(prevKeys => [enhancedResponse, ...prevKeys]);

      // Actualizar estadísticas
      setStatistics(prevStats => ({
        ...prevStats,
        total: (prevStats?.total || 0) + 1,
        successful: (prevStats?.successful || 0) + 1,
        uploadedToCtm: enhancedResponse.uploadedToCtm ? (prevStats?.uploadedToCtm || 0) + 1 : (prevStats?.uploadedToCtm || 0)
      }));

      return enhancedResponse;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener llaves por usuario con paginación del servidor
   */
  const getKeysByUser = useCallback(async (userId, filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      // Usar paginación del servidor si se especifica
      const requestFilters = {
        page: filters.page || currentPage,
        limit: filters.limit || keysPerPage,
        ...filters
      };

      const response = await keyService.getKeysByUser(userId, requestFilters);

      // Procesar las llaves para asegurar que tengan el algoritmo correcto
      const processedKeys = response.keys.map(key => {
        // Si el tipo es 'hex_key' pero no hay algoritmo, intentar inferirlo o usar un valor por defecto
        if (key.type === 'hex_key' && !key.algorithm) {
          // Intentar inferir el algoritmo basado en el tamaño de bytes
          let inferredAlgorithm = 'AES'; // Por defecto
          if (key.num_bytes <= 32) {
            inferredAlgorithm = 'AES';
          } else if (key.num_bytes <= 64) {
            inferredAlgorithm = 'HMAC';
          } else if (key.num_bytes <= 512) {
            inferredAlgorithm = 'RSA';
          }

          return {
            ...key,
            algorithm: inferredAlgorithm,
            type: inferredAlgorithm
          };
        }

        // Si ya tiene algoritmo, asegurar que esté en ambos campos
        return {
          ...key,
          algorithm: key.algorithm || key.type,
          type: key.algorithm || key.type
        };
      });

      // Actualizar estado de paginación
      setCurrentPage(requestFilters.page);
      setTotalKeys(response.total || 0);
      setTotalPages(Math.ceil((response.total || 0) / keysPerPage));

      // Reemplazar las llaves con las de la página actual
      setKeys(processedKeys);

      // Actualizar estadísticas
      setStatistics({
        total: response.total,
        successful: response.successful,
        failed: response.failed,
        uploadedToCtm: response.uploadedToCtm,
      });

      return { ...response, keys: processedKeys };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, keysPerPage]);

  /**
   * Cambiar a la página siguiente
   */
  const goToNextPage = useCallback(async (userId) => {
    if (currentPage < totalPages) {
      await getKeysByUser(userId, { page: currentPage + 1 });
    }
  }, [currentPage, totalPages, getKeysByUser]);

  /**
   * Cambiar a la página anterior
   */
  const goToPrevPage = useCallback(async (userId) => {
    if (currentPage > 1) {
      await getKeysByUser(userId, { page: currentPage - 1 });
    }
  }, [currentPage, getKeysByUser]);

  /**
   * Ir a una página específica
   */
  const goToPage = useCallback(async (userId, page) => {
    if (page >= 1 && page <= totalPages) {
      await getKeysByUser(userId, { page });
    }
  }, [totalPages, getKeysByUser]);

  /**
   * Obtener estadísticas de llaves
   */
  const getStatistics = useCallback(async (userId) => {
    try {
      setError(null);
      
      const response = await keyService.getKeyStatistics(userId);
      setStatistics(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, []);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Limpiar llaves
   */
  const clearKeys = useCallback(() => {
    setKeys([]);
    setStatistics(null);
    setCurrentPage(1);
    setTotalPages(1);
    setTotalKeys(0);
  }, []);

  /**
   * Filtrar llaves localmente
   */
  const filterKeys = useCallback((filters) => {
    let filteredKeys = [...keys];
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredKeys = filteredKeys.filter(key => 
        key.name.toLowerCase().includes(searchTerm) ||
        key.displayName.toLowerCase().includes(searchTerm)
      );
    }
    
    if (filters.algorithm) {
      filteredKeys = filteredKeys.filter(key => key.algorithm === filters.algorithm);
    }
    
    if (filters.status) {
      filteredKeys = filteredKeys.filter(key => key.status === filters.status);
    }
    
    if (filters.uploadedToCtm !== undefined) {
      filteredKeys = filteredKeys.filter(key => key.uploadedToCtm === filters.uploadedToCtm);
    }
    
    return filteredKeys;
  }, [keys]);

  /**
   * Eliminar una llave
   */
  const deleteKey = useCallback(async (keyId) => {
    try {
      setIsLoading(true);
      setError(null);

      await keyService.deleteKey(keyId);

      // Actualizar la lista de llaves removiendo la llave eliminada
      setKeys(prevKeys => prevKeys.filter(key => key.id !== keyId));

      // Actualizar estadísticas
      setStatistics(prevStats => ({
        ...prevStats,
        total: Math.max(0, (prevStats?.total || 0) - 1)
      }));

      return true;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Actualizar una llave (cambiar CTM Key ID)
   */
  const updateKey = useCallback(async (ctmKeyId) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await keyService.updateKey(ctmKeyId);

      // Recargar la lista de llaves para obtener los datos actualizados
      // La actualización se reflejará automáticamente
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener versiones de una llave
   */
  const getKeyVersions = useCallback(async (keyId) => {
    try {
      setError(null);

      const response = await keyService.getKeyVersions(keyId);
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, []);

  /**
   * Obtener llaves por CTM específico
   */
  const getKeysByCtm = useCallback(async (ctmId, filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      // Usar paginación del servidor si se especifica
      const requestFilters = {
        page: filters.page || currentPage,
        limit: filters.limit || keysPerPage,
        ctmId,
        ...filters
      };

      const response = await keyService.getKeysByCtm(requestFilters);

      // Procesar las llaves para asegurar que tengan el algoritmo correcto
      const processedKeys = response.keys.map(key => {
        if (key.type === 'hex_key' && !key.algorithm) {
          let inferredAlgorithm = 'AES';
          if (key.num_bytes <= 32) {
            inferredAlgorithm = 'AES';
          } else if (key.num_bytes <= 64) {
            inferredAlgorithm = 'HMAC';
          } else if (key.num_bytes <= 512) {
            inferredAlgorithm = 'RSA';
          }

          return {
            ...key,
            algorithm: inferredAlgorithm,
            type: inferredAlgorithm
          };
        }

        return {
          ...key,
          algorithm: key.algorithm || key.type,
          type: key.algorithm || key.type
        };
      });

      // Actualizar estado de paginación
      setCurrentPage(requestFilters.page);
      setTotalKeys(response.total || 0);
      setTotalPages(Math.ceil((response.total || 0) / keysPerPage));

      // Reemplazar las llaves con las de la página actual
      setKeys(processedKeys);

      // Actualizar estadísticas
      setStatistics({
        total: response.total,
        successful: response.successful,
        failed: response.failed,
        uploadedToCtm: response.uploadedToCtm,
      });

      return { ...response, keys: processedKeys };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, keysPerPage]);

  /**
   * Obtener llaves por HSM específico
   */
  const getKeysByHsm = useCallback(async (hsmId, filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await hsmKeyService.getKeysByHsm(hsmId, filters);

      // Las llaves HSM ya vienen con el formato correcto
      const processedKeys = response.map(key => ({
        ...key,
        // Asegurar compatibilidad con el formato de llaves CTM
        algorithm: key.keyType || key.algorithm,
        type: key.keyType || key.type,
        num_bytes: key.keySize ? key.keySize / 8 : null, // Convertir bits a bytes
      }));

      // Para HSM, no hay paginación del servidor por ahora
      setKeys(processedKeys);
      setTotalKeys(processedKeys.length);
      setTotalPages(1);
      setCurrentPage(1);

      // Estadísticas básicas para HSM
      setStatistics({
        total: processedKeys.length,
        successful: processedKeys.filter(k => k.status === 'ACTIVE').length,
        failed: processedKeys.filter(k => k.status === 'FAILED').length,
        uploadedToCtm: 0, // HSM no usa CTM
      });

      return { keys: processedKeys, total: processedKeys.length };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener llaves por servicio (CTM o HSM)
   */
  const getKeysByService = useCallback(async (service, filters = {}) => {
    if (!service) {
      throw new Error('Service is required');
    }

    const serviceType = typeof service === 'object' ? service.type :
      (service.startsWith('HSM') ? 'HSM' : 'CTM');
    const serviceId = typeof service === 'object' ? service.id : null;

    if (serviceType === 'HSM' && serviceId) {
      return await getKeysByHsm(serviceId, filters);
    } else if (serviceType === 'CTM' && serviceId) {
      return await getKeysByCtm(serviceId, filters);
    } else {
      throw new Error('Invalid service type or missing service ID');
    }
  }, [getKeysByCtm, getKeysByHsm]);

  return {
    // Estado
    keys,
    statistics,
    isLoading,
    error,
    
    // Estado de paginación
    currentPage,
    totalPages,
    totalKeys,
    keysPerPage,

    // Acciones
    uploadToCTM,
    getKeysByUser,
    getKeysByCtm,
    getKeysByHsm,
    getKeysByService,
    getStatistics,
    deleteKey,
    updateKey,
    getKeyVersions,
    clearError,
    clearKeys,
    filterKeys,
    
    // Acciones de paginación
    goToNextPage,
    goToPrevPage,
    goToPage,
  };
};

/**
 * useKeyUpload Hook
 * Hook específico para subida de llaves
 */
export const useKeyUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);

  /**
   * Subir llave con progreso
   */
  const uploadKey = useCallback(async (keyData) => {
    try {
      setIsUploading(true);
      setError(null);
      setResult(null);
      setUploadProgress(0);

      // Simular progreso
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await keyService.uploadToCTM(keyData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      setResult(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      setUploadProgress(0);
      throw error;
    } finally {
      setIsUploading(false);
    }
  }, []);

  /**
   * Resetear estado
   */
  const reset = useCallback(() => {
    setIsUploading(false);
    setUploadProgress(0);
    setError(null);
    setResult(null);
  }, []);

  return {
    // Estado
    isUploading,
    uploadProgress,
    error,
    result,
    
    // Acciones
    uploadKey,
    reset,
  };
};
