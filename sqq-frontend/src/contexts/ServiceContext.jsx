/**
 * Service Context
 * Contexto global para manejar el servicio seleccionado por el usuario
 */

import { createContext, useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useServices } from '../hooks/useServices';
import { useAuth } from '../hooks/useAuth';

const ServiceContext = createContext();

export const useServiceContext = () => {
  const context = useContext(ServiceContext);
  if (!context) {
    throw new Error('useServiceContext must be used within a ServiceProvider');
  }
  return context;
};

export const ServiceProvider = ({ children }) => {
  const [selectedService, setSelectedService] = useState(null);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const { getUserServices, getFormattedServices } = useServices();
  const { user: currentUser } = useAuth();

  // Cargar servicios del usuario
  useEffect(() => {
    const loadUserServices = async () => {
      if (currentUser && currentUser.id && currentUser.role === 'USER') {
        try {
          setIsLoading(true);
          await getUserServices();
          const formatted = getFormattedServices();
          setAvailableServices(formatted);
          
          // Seleccionar el primer servicio disponible si no hay uno seleccionado
          if (!selectedService && formatted.length > 0) {
            setSelectedService(formatted[0]);
          }
        } catch (error) {
          console.error('Error loading user services:', error);
          setAvailableServices([]);
        } finally {
          setIsLoading(false);
        }
      } else {
        // Si no es usuario, limpiar servicios
        setAvailableServices([]);
        setSelectedService(null);
      }
    };

    loadUserServices();
  }, [currentUser, getUserServices, getFormattedServices]);

  const handleServiceChange = (service) => {
    setSelectedService(service);
    // Guardar en localStorage para persistencia
    if (service) {
      localStorage.setItem('selectedService', JSON.stringify(service));
    } else {
      localStorage.removeItem('selectedService');
    }
  };

  // Restaurar servicio seleccionado desde localStorage
  useEffect(() => {
    const savedService = localStorage.getItem('selectedService');
    if (savedService && !selectedService) {
      try {
        const parsedService = JSON.parse(savedService);
        // Verificar que el servicio guardado aún esté disponible
        const isServiceAvailable = availableServices.some(
          service => service.id === parsedService.id && service.type === parsedService.type
        );
        if (isServiceAvailable) {
          setSelectedService(parsedService);
        }
      } catch (error) {
        console.error('Error parsing saved service:', error);
        localStorage.removeItem('selectedService');
      }
    }
  }, [availableServices, selectedService]);

  const value = {
    selectedService,
    availableServices,
    isLoading,
    handleServiceChange,
    // Funciones de utilidad
    getSelectedServiceType: () => selectedService?.type || null,
    getSelectedServiceId: () => selectedService?.id || null,
    getSelectedServiceName: () => selectedService?.name || null,
    hasServices: () => availableServices.length > 0,
    isServiceSelected: () => selectedService !== null,
  };

  return (
    <ServiceContext.Provider value={value}>
      {children}
    </ServiceContext.Provider>
  );
};

ServiceProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ServiceContext;
