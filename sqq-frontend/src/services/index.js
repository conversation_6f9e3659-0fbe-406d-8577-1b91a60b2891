/**
 * Services Index
 * Punto de entrada centralizado para todos los servicios
 */

// Core services
export { default as httpClient, HttpError } from './core/httpClient.js';

// Configuration
export { API_CONFIG, API_ENDPOINTS } from './config/apiConfig.js';

// Auth services
export { default as authService } from './auth/authService.js';

// User services
export { default as userService } from './users/userService.js';

// Key services
export { default as keyService } from './keys/keyService.js';

// CTM services
export { default as ctmService } from './ctm/ctmService.js';

// HSM services
export { default as hsmService } from './hsm/hsmService.js';
export { default as hsmKeyService } from './hsm/hsmKeyService.js';

// Convenience exports for common operations
export const api = {
  auth: {
    login: (email, password) => import('./auth/authService.js').then(m => m.default.login(email, password)),
    logout: () => import('./auth/authService.js').then(m => m.default.logout()),
    getProfile: () => import('./auth/authService.js').then(m => m.default.getProfile()),
    refreshToken: (token) => import('./auth/authService.js').then(m => m.default.refreshToken(token)),
    isTokenValid: () => import('./auth/authService.js').then(m => m.default.isTokenValid()),
    getUserRole: () => import('./auth/authService.js').then(m => m.default.getUserRole()),
  },
  
  users: {
    getAll: () => import('./users/userService.js').then(m => m.default.getAllUsers()),
    create: (userData) => import('./users/userService.js').then(m => m.default.createUser(userData)),
    update: (id, data) => import('./users/userService.js').then(m => m.default.updateUser(id, data)),
    getById: (id) => import('./users/userService.js').then(m => m.default.getUserById(id)),
  },
  
  keys: {
    uploadToCTM: (keyData) => import('./keys/keyService.js').then(m => m.default.uploadToCTM(keyData)),
    getByUser: (userId, filters) => import('./keys/keyService.js').then(m => m.default.getKeysByUser(userId, filters)),
    getStatistics: (userId) => import('./keys/keyService.js').then(m => m.default.getKeyStatistics(userId)),
    getVersions: (keyId) => import('./keys/keyService.js').then(m => m.default.getKeyVersions(keyId)),
  },

  ctms: {
    getAll: (filters) => import('./ctm/ctmService.js').then(m => m.default.getAllCtms(filters)),
    getUserCtms: () => import('./ctm/ctmService.js').then(m => m.default.getUserCtms()),
    getById: (id) => import('./ctm/ctmService.js').then(m => m.default.getCtmById(id)),
    create: (ctmData) => import('./ctm/ctmService.js').then(m => m.default.createCtm(ctmData)),
    update: (id, data) => import('./ctm/ctmService.js').then(m => m.default.updateCtm(id, data)),
    delete: (id) => import('./ctm/ctmService.js').then(m => m.default.deleteCtm(id)),
    assignToUser: (userId, ctmIds) => import('./ctm/ctmService.js').then(m => m.default.assignCtmsToUser(userId, ctmIds)),
  },

  hsms: {
    getAll: (filters) => import('./hsm/hsmService.js').then(m => m.default.getAllHsms(filters)),
    getUserHsms: () => import('./hsm/hsmService.js').then(m => m.default.getUserHsms()),
    getById: (id) => import('./hsm/hsmService.js').then(m => m.default.getHsmById(id)),
    create: (hsmData) => import('./hsm/hsmService.js').then(m => m.default.createHsm(hsmData)),
    update: (id, data) => import('./hsm/hsmService.js').then(m => m.default.updateHsm(id, data)),
    delete: (id) => import('./hsm/hsmService.js').then(m => m.default.deleteHsm(id)),
    assignToUser: (userId, hsmIds) => import('./hsm/hsmService.js').then(m => m.default.assignHsmsToUser(userId, hsmIds)),
  },

  hsmKeys: {
    create: (keyData) => import('./hsm/hsmKeyService.js').then(m => m.default.createHsmKey(keyData)),
    getUserKeys: (filters) => import('./hsm/hsmKeyService.js').then(m => m.default.getUserHsmKeys(filters)),
    getAll: (filters) => import('./hsm/hsmKeyService.js').then(m => m.default.getAllHsmKeys(filters)),
    getById: (id) => import('./hsm/hsmKeyService.js').then(m => m.default.getHsmKeyById(id)),
    getByHsm: (hsmId, filters) => import('./hsm/hsmKeyService.js').then(m => m.default.getKeysByHsm(hsmId, filters)),
  },
};
