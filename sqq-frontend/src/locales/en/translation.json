{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "clear": "Clear", "refresh": "Refresh", "view": "View", "download": "Download", "upload": "Upload", "create": "Create", "update": "Update", "remove": "Remove", "yes": "Yes", "no": "No", "user": "User", "actions": "Action", "unknownError": "Unknown error", "ctmKeyNotFound": "CTM Key ID not found for this key", "passwordChangeError": "Error changing password. Please try again.", "autoCloseMessage": "It will close automatically...", "pagination": {"of": "of", "keys": "keys", "key": "key", "page": "Page", "ofPages": "of", "total": "total"}, "tooltips": {"serviceSelector": {"title": "Service Selector", "content": "Select a service (CTM or HSM) to show only associated keys."}, "selfCertReport": {"title": "Self-Certification Report", "content": "Key validation results: entropy, source and quantum fidelity."}, "entropyStatus": {"title": "Entropy Status", "content": "Indicates whether the key's randomness quality is suitable for secure use."}, "source": {"title": "Source", "content": "Indicates where the key generation comes from."}, "quantumFidelity": {"title": "Quantum Fidelity", "content": "Value between 0 and 1 that indicates the precision of key generation relative to its quantum source. A value close to 1 means very high quality."}, "ctmKeyId": {"title": "CTM Key ID", "content": "Key identifier in CTM (CipherTrust Manager)."}, "localId": {"title": "Local ID", "content": "Internal key identifier in our platform (differs from CTM Key ID)."}}, "serviceSelector": {"title": "Service", "current": "Current"}}, "navigation": {"dashboard": "Dashboard", "keys": "My Keys", "profile": "My Profile", "settings": "Settings", "logout": "Logout", "darkMode": "Dark Mode", "lightMode": "Light Mode"}, "dashboard": {"welcome": "Welcome, {{name}}", "subtitle": "Here you can manage your quantum keys and update your personal information.", "stats": {"myKeys": "My Keys", "activeKeys": "Active Keys", "totalKeys": "Total Keys", "pendingKeys": "Pending Keys", "lastAccess": "Last Access", "keysGenerated": "Quantum keys generated.", "keysActive": "Keys in operation.", "keysPending": "Keys pending upload.", "lastLoginDate": "Last login date."}, "miniStats": {"successful": "Successful", "failed": "Failed", "inCTM": "In CTM", "successRate": "% Success"}, "charts": {"keysGenerated": {"title": "Generated Keys", "subtitle": "Last 7 days"}, "keyStatus": {"title": "Key Status", "subtitle": "Current distribution", "successful": "Successful", "failed": "Failed", "inCTM": "In CTM", "total": "Total"}, "algorithms": {"title": "Used Algorithms", "subtitle": "Distribution by type", "keys": "keys"}, "weeklyEvolution": {"title": "Weekly Evolution", "subtitle": "Last 4 weeks", "totalGenerated": "Total Generated", "weeklyAverage": "Weekly Average", "keysGenerated": "keys generated", "week1": "Week 1", "week2": "Week 2", "week3": "Week 3", "week4": "Week 4"}}, "messages": {"passwordUpdated": "Password Updated!", "passwordUpdatedMessage": "Your password has been changed successfully. Your account is now more secure."}}, "admin": {"welcome": "Welcome to Admin Panel", "subtitle": "Manage users, quantum keys and monitor the system", "navigation": {"users": "Users", "keys": "Keys", "ctms": "CTMs", "hsms": "HSMs"}, "profile": {"title": "My Administrator Profile", "subtitle": "Manage your personal information and account settings as administrator"}, "keys": {"title": "Quantum Keys Management", "subtitle": "Manage and monitor all quantum keys in the QRNG system", "description": "From here you can monitor the status of all quantum keys generated by users, review usage statistics, and manage global system key configurations.", "features": {"monitoring": {"title": "Real-Time Monitoring", "description": "Monitor the status and performance of all active quantum keys"}, "analytics": {"title": "Analytics & Statistics", "description": "Get detailed insights about system usage and efficiency"}, "management": {"title": "Centralized Management", "description": "Manage global configurations and security policies"}}, "comingSoon": "Coming Soon", "developmentNote": "This section is under active development and will be available in the next system update."}, "stats": {"totalUsers": "Total Users", "totalUsersDesc": "Users registered in the system.", "activeKeys": "Active Keys", "activeKeysDesc": "<PERSON> successfully uploaded to CTM.", "activeUsers": "Active Users", "activeUsersDesc": "Users with active status.", "totalKeys": "Total Keys", "successfulKeys": "Successful", "failedKeys": "Failed", "inCTM": "In CTM", "total": "Total", "active": "Active", "failed": "Failed", "pending": "Pending", "usersWithKeys": "Users with keys", "searchPlaceholder": "Search keys, users...", "allUsers": "All users", "allStatuses": "All statuses", "allAlgorithms": "All algorithms", "clearFilters": "Clear filters", "systemKeys": "System Keys", "noKeysFound": "No keys found", "adjustFilters": "Try adjusting the search filters", "userSummary": "User Summary", "viewKeyDetail": "View key detail", "viewUserKeys": "View all user keys", "viewUserKeysTitle": "View user keys", "user": "User", "algorithm": "Algorithm", "bytes": "Bytes", "created": "Created", "noName": "No name"}, "users": {"title": "User Management", "subtitle": "Manage users of the QRNG Quantum system", "createUser": "Add User", "active": "Active", "inactive": "Inactive", "admin": "Admin", "user": "User", "viewKeys": "View user keys", "editUser": "Edit user", "viewDetail": "View Detail", "deleteUser": "Delete user", "keyStats": "Key Statistics", "create": {"title": "Create New User", "subtitle": "Add a new user to the system with their service configurations", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "company": "Company", "role": "Role", "roles": {"user": "User", "admin": "Administrator"}, "passwordRequirements": "Password Requirements", "minLength": "Minimum 8 characters", "alphanumeric": "Alphanumeric characters and symbols allowed", "serviceConfig": "Service Configuration", "serviceSelection": "Service Selection", "selectService": "Select Service", "selectServicePlaceholder": "Select a CTM or HSM...", "ctmGroup": "CipherTrust Managers (CTMs)", "hsmGroup": "Hardware Security Modules (HSMs)", "ctm": {"title": "CipherTrust Manager", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain"}, "seqrng": {"title": "SEQRNG", "ipAddress": "IP Address", "apiToken": "API Token"}, "creating": "Creating User...", "createButton": "Create User", "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "passwordRequired": "Password is required"}}, "delete": {"title": "Delete User", "subtitle": "Confirm permanent user deletion", "userInfo": "User Information", "warningTitle": "Irreversible Action", "confirmQuestion": "Delete User?", "confirmMessage": "Are you sure you want to delete the user", "warningMessage": "This action cannot be undone", "deleteButton": "Delete User"}, "edit": {"title": "Edit User", "subtitle": "Modify user information and service configurations", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "company": "Company", "role": "Role", "status": "Status", "serviceConfig": "Service Configuration", "saveButton": "Save Changes", "saving": "Saving Changes...", "roles": {"user": "User", "admin": "Administrator"}, "statusOptions": {"active": "Active", "inactive": "Inactive"}, "ctm": {"title": "CipherTrust Manager", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain"}, "seqrng": {"title": "SEQRNG", "ipAddress": "IP Address", "apiToken": "API Token"}}, "keys": {"title": "User Keys", "subtitle": "Manage and view user quantum keys", "totalKeys": "Total keys", "active": "Active", "failed": "Failed", "viewDetail": "View Detail", "close": "Close", "loading": "Loading user keys...", "statusActive": "Active in CTM", "statusInactive": "Inactive", "statusPending": "Pending", "showing": "Showing", "of": "of", "keys": "keys", "detail": {"title": "Key Details", "closeButton": "Close"}}, "detail": {"title": "User Details", "subtitle": "Complete user information and configurations", "personalInfo": "Personal Information", "fullName": "Full Name", "email": "Email", "company": "Company", "role": "Role", "status": "Status", "administrator": "Administrator", "user": "User", "active": "Active", "inactive": "Inactive", "cipherTrustManager": "CipherTrust Manager", "seqrng": "SEQRNG", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain", "apiToken": "API Token"}}, "ctms": {"title": "CTM Management", "subtitle": "Manage system CipherTrust Managers", "create": "Create CTM", "searchPlaceholder": "Search CTMs...", "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "maintenance": "Maintenance"}, "list": {"title": "Registered CTMs", "empty": "No CTMs registered in the system"}, "status": {"active": "Active", "inactive": "Inactive", "maintenance": "Maintenance"}, "form": {"name": "CTM Name", "namePlaceholder": "Main CTM", "description": "Description", "descriptionPlaceholder": "CTM description...", "ipAddress": "IP Address", "port": "Port", "version": "Version", "status": "Status"}, "connection": {"title": "CipherTrust Manager", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain"}, "createModal": {"title": "Create New CTM", "subtitle": "Add a new CipherTrust Manager to the system", "configTitle": "CTM Configuration", "submit": "Create CTM"}, "editModal": {"title": "Edit CTM", "subtitle": "Modify CipherTrust Manager information", "submit": "Save Changes"}, "deleteModal": {"title": "Delete CTM", "subtitle": "Confirm CipherTrust Manager deletion", "warning": "Warning!", "warningText": "This action will permanently delete the CTM from the system. This operation cannot be undone.", "confirm": "Delete CTM"}, "editCtm": "Edit CTM", "viewCtm": "View CTM", "deleteCtm": "Delete CTM", "detailCtm": "CTM Details", "detailCtmDescription": "Complete information of the CipherTrust Manager and its configurations", "messages": {"created": "CTM Created!", "createdMessage": "The CipherTrust Manager has been successfully created in the system.", "updated": "CTM Updated!", "updatedMessage": "The CipherTrust Manager has been successfully updated.", "deleted": "CTM Deleted!", "deletedMessage": "The CipherTrust Manager has been successfully deleted from the system."}}, "hsms": {"title": "HSM Management", "subtitle": "Manage system Hardware Security Modules", "create": "Create HSM", "searchPlaceholder": "Search HSMs...", "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "maintenance": "Maintenance"}, "list": {"title": "Registered HSMs", "empty": "No HSMs registered in the system"}, "status": {"active": "Active", "inactive": "Inactive", "maintenance": "Maintenance"}, "form": {"name": "HSM Name", "namePlaceholder": "Main HSM", "description": "Description", "descriptionPlaceholder": "HSM description...", "ipAddress": "IP Address", "port": "Port", "version": "Version", "status": "Status"}, "connection": {"title": "Hardware Security Module", "ipAddress": "IP Address", "username": "Username", "password": "Password", "domain": "Domain"}, "createModal": {"title": "Create New HSM", "subtitle": "Add a new Hardware Security Module to the system", "configTitle": "HSM Configuration", "submit": "Create HSM"}, "editModal": {"title": "Edit HSM", "subtitle": "Modify Hardware Security Module information", "configTitle": "HSM Configuration", "submit": "Save Changes"}, "deleteModal": {"title": "Delete HSM", "subtitle": "Confirm Hardware Security Module deletion", "warning": "Warning!", "warningText": "This action will permanently delete the HSM from the system. This operation cannot be undone.", "confirm": "Delete HSM"}, "editHsm": "Edit HSM", "viewHsm": "View HSM", "deleteHsm": "Delete HSM", "detailHsm": "HSM Details", "detailHsmDescription": "Complete information of the Hardware Security Module and its configurations", "messages": {"created": "HSM Created!", "createdMessage": "The Hardware Security Module has been successfully created in the system.", "updated": "HSM Updated!", "updatedMessage": "The Hardware Security Module has been successfully updated.", "deleted": "HSM Deleted!", "deletedMessage": "The Hardware Security Module has been successfully deleted from the system."}}}, "keys": {"title": "My Quantum Keys", "subtitle": "Manage your quantum keys, view details and upload new keys to the CTM system.", "uploadNew": "Create New Key", "createNewVersion": "Create New Version", "noKeys": "You have no registered keys. Create your first key!", "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "uploaded": "Uploaded to CTM", "uploaded_to_ctm": "Uploaded to CTM"}, "details": {"name": "Name", "algorithm": "Algorithm", "created": "Created", "ctmKeyId": "CTM Key ID", "localId": "Local ID", "type": "Type", "status": "Status"}, "actions": {"viewDetail": "View key details", "deleteKey": "Delete key", "uploadToCTM": "Upload to CTM", "updateKey": "Update key"}, "noName": "Unnamed key", "upload": {"title": "Create New Key to CTM", "titleHSM": "Create New Key to HSM", "subtitle": "Create a new secure quantum key", "subtitleHSM": "Create a new secure quantum key for HSM", "keyInfo": "Key Information", "technicalConfig": "Technical Configuration", "algorithmLimits": "Algorithm Limits", "keyName": "Key Name", "keyNamePlaceholder": "my-encryption-key-001", "keyNameHelp": "Only letters, numbers, hyphens and underscores", "algorithm": "Algorithm", "keySize": "Number of Bytes", "keySizeHelp": "Between 1 and 1024 bytes", "maxBytes": "Max. {{bytes}} bytes", "maxPermitted": "Maximum permitted for {{algorithm}}: {{bytes}} bytes", "exportable": "Exportable", "maxBytesShort": "Max. {{bytes}} bytes", "errorMaxBytes": "{{algorithm}} allows maximum {{bytes}} bytes", "allowedValues": "Allowed values: {{values}} bytes", "selectAllowedValue": "Select one of the allowed values for {{algorithm}}", "autoGeneration": "Automatic Key Generation", "autoGenerationDesc": "Cryptographic material will be automatically generated securely using quantum algorithms.", "message": "Message", "messagePlaceholder": "Enter your message here...", "messageHelp": "Message required for HSM keys", "uploading": "Uploading...", "uploadButton": "Upload to CTM"}, "loading": "Loading keys...", "modals": {"success": {"title": "🔐 Key Uploaded Successfully!", "message": "Your key \"{{name}}\" ({{algorithm}} - {{size}} bytes) has been generated and uploaded to CipherTrust Manager successfully. It's ready to use!", "messageGeneric": "Your key has been uploaded successfully to CipherTrust Manager."}, "error": {"title": "⚠️ Key Upload Error", "message": "Could not upload key \"{{name}}\" ({{algorithm}} - {{size}} bytes). {{error}}. Please check your connection and try again.", "messageGeneric": "An error occurred while trying to upload the key to CipherTrust Manager."}}, "detail": {"title": "Key Details", "status": "Status", "statusMessages": {"active": "The key is active in the system", "inactive": "The key is inactive in the system", "pending": "Processing and validating"}, "basicInfo": "Basic Information", "name": "Name", "ctmKeyId": "CTM Key ID", "localId": "Local ID", "typeAlgorithm": "Type/Algorithm", "numBytes": "Number of Bytes", "exportable": "Exportable", "yes": "Yes", "no": "No", "selfCertReport": "Self-certification Report", "entropyStatus": "Entropy Status", "source": "Source", "quantumFidelity": "Quantum Fidelity", "dates": "Dates", "created": "Created", "updated": "Updated", "uploadedToCTM": "Uploaded to CTM", "keyMaterial": "Key Material", "base64": "Base64", "sensitiveInfo": "Sensitive information", "technicalInfo": "Technical Information", "errorMessage": "Error Message", "closeButton": "Close", "copyTooltip": "Copy"}, "delete": {"title": "Confirm Deletion", "subtitle": "This action is irreversible", "confirmQuestion": "Are you sure you want to delete the key?", "warningMessage": "This action cannot be undone. The key will be permanently deleted from the system.", "deleteButton": "Delete Key"}, "update": {"title": "Confirm Update", "subtitle": "A new CTM Key ID will be generated", "confirmQuestion": "Are you sure you want to update the key?", "warningMessage": "When updating the key, a new CTM Key ID will be generated. The previous ID will be replaced.", "whatWillHappen": "What will happen?", "consequences": {"newVersion": "A new version of the key will be automatically generated", "newVersionActive": "The new version will remain in \"Active\" status", "previousVersionsInactive": "All previous versions will change to \"Inactive\"", "listWillUpdate": "The list will be automatically updated"}, "updateButton": "Update Key", "success": {"title": "Key Updated Successfully!", "message": "The key \"{{name}}\" has been updated successfully. A new version has been generated with \"Active\" status and all previous versions have been marked as \"Inactive\"."}}, "versions": {"title": "Versions of {{keyName}}", "subtitle": "Review and select a key version", "motherKeys": "<PERSON>", "searchPlaceholder": "Search version number", "availableKeys": "{{filtered}} of {{total}} mother keys available", "createNewVersion": {"title": "Create New Version", "subtitle": "Select a mother key to create a new version", "selectKey": "Select Key", "noMotherKeys": "No mother keys available to create versions", "createButton": "Create New Version", "selectKeyFirst": "You must select a key first"}, "version": "Version", "results": "Results", "totalVersions": "Total Versions", "versionNumber": "Version {{number}}", "columns": {"version": "Version", "state": "State", "creationDate": "Creation Date", "lastModified": "Last Modified", "activationDate": "Activation Date"}, "to": "to", "of": "of", "keyVersions": "Key Versions", "perPage": "per page", "selectButton": "Select", "states": {"active": "Active", "inactive": "Inactive"}, "loading": "Loading versions...", "preparing": "Preparing versions...", "noVersionsFound": "No versions found for this key", "noKeyData": "No key data available", "reloadingAfterUpdate": "Reloading after update...", "keyUpdatedSuccessfully": "Key updated successfully", "newVersionActive": "The new version will appear as \"Active\" and the previous ones as \"Inactive\"", "previousPage": "Previous page", "nextPage": "Next page", "versionOf": "version of", "versionsOf": "versions of"}}, "profile": {"title": "My Profile", "subtitle": "Manage your personal information and account settings", "information": "Profile Information", "informationSubtitle": "Personal and account data", "changePassword": "Change Password", "editProfile": "Edit Profile", "fullName": "Full Name", "email": "Email", "company": "Company", "registrationDate": "Registration Date", "notSpecified": "Not specified", "loadingProfile": "Loading profile information...", "changePasswordModal": {"title": "Change Password", "subtitle": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "currentPasswordPlaceholder": "Enter your current password", "newPasswordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Confirm your new password", "requirements": "Password Requirements", "requirementsList": {"minLength": "Minimum 8 characters", "alphanumeric": "Alphanumeric characters and symbols allowed", "different": "Must be different from current password"}, "changeButton": "Change Password", "changingButton": "Changing...", "errors": {"currentRequired": "Current password is required", "confirmRequired": "Must confirm the new password", "passwordsNotMatch": "Passwords do not match", "samePassword": "New password must be different from current password"}}, "editProfileModal": {"title": "Edit My Profile", "subtitle": "Update your personal information to keep your profile up to date", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "serviceConfig": "Service Configuration", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "company": "Company (optional)", "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "emailPlaceholder": "Enter your email", "companyPlaceholder": "Enter your company", "serviceConfigNote": "CipherTrust Manager and SEQRNG configuration is managed by the system administrator. If you need to modify these settings, contact your administrator.", "selectAvatar": "Select Avatar", "currentAvatar": "Current avatar", "selectAvatarDescription": "Select a new avatar from the available options", "saveButton": "Save Changes", "savingButton": "Saving...", "updateError": "Error updating profile"}, "personalInfo": "Personal Information", "contactInfo": "Contact Information", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "company": "Company", "registrationDate": "Registration Date"}, "placeholders": {"firstName": "Enter your first name", "lastName": "Enter your last name", "email": "Enter your email", "company": "Enter your company"}, "edit": {"title": "Edit My Profile", "subtitle": "Update your personal information to keep your profile up to date", "saveChanges": "Save Changes", "saving": "Saving..."}, "password": {"title": "Change Password", "subtitle": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "requirements": "Password Requirements", "minLength": "Minimum 8 characters", "alphanumeric": "Alphanumeric characters and symbols allowed", "different": "Must be different from current password", "changing": "Changing...", "change": "Change Password"}}, "language": {"selector": "Language", "spanish": "Español", "english": "English"}, "errors": {"generic": "An unexpected error occurred", "network": "Connection error", "unauthorized": "Unauthorized", "forbidden": "Access denied", "notFound": "Not found", "validation": "Validation error", "required": "This field is required", "invalidEmail": "Invalid email", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters"}, "messages": {"profileUpdated": "Profile updated successfully", "passwordChanged": "Password changed successfully", "keyUploaded": "Key uploaded successfully", "keyDeleted": "Key deleted successfully", "confirmLogout": "Logout?", "confirmLogoutSubtitle": "This action will close your current session", "confirmLogoutQuestion": "Are you sure you want to close your session?", "confirmLogoutWarning": "You will need to log in again to access the system.", "confirmDelete": "Are you sure you want to delete this key?"}}